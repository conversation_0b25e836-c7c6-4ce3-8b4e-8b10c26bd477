# Database Configuration Guide

## Overview

This document provides comprehensive instructions for setting up and configuring PostgreSQL database for the AMS application.

## Prerequisites

### PostgreSQL Installation

Install PostgreSQL on your system:

#### Windows

```bash
# Using Chocolatey
choco install postgresql

# Or download from https://www.postgresql.org/download/windows/
```

#### macOS

```bash
# Using Homebrew
brew install postgresql
brew services start postgresql
```

#### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Database Setup

### 1. Create Database and User

Connect to PostgreSQL as superuser and create the application database:

```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database
CREATE DATABASE ams_development;

-- Create user
CREATE USER ams_user WITH PASSWORD 'ams_dev_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE ams_development TO ams_user;

-- Grant schema privileges
\c ams_development
GRANT ALL ON SCHEMA public TO ams_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ams_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ams_user;

-- Exit
\q
```

### 2. Connection String Configuration

#### Development Environment

Update `appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ams_development;Username=ams_user;Password=ams_dev_password"
  }
}
```

#### Production Environment

Use environment variables for security:

```bash
export ConnectionStrings__DefaultConnection="Host=your-server;Database=ams_development;Username=ams_user;Password=ams_dev_password;SSL Mode=Require"
```

Or in `appsettings.Production.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=your-server;Database=ams_development;Username=ams_user;Password=ams_dev_password;SSL Mode=Require"
  }
}
```

## Database Migrations

### Creating Migrations

```bash
# Create a new migration
dotnet ef migrations add MigrationName --project src/AMS.Infrastructure --startup-project src/AMS.API

# Remove last migration (if not applied)
dotnet ef migrations remove --project src/AMS.Infrastructure --startup-project src/AMS.API
```

### Applying Migrations

```bash
# Apply migrations to database
dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API

# Apply specific migration
dotnet ef database update MigrationName --project src/AMS.Infrastructure --startup-project src/AMS.API

# Rollback to specific migration
dotnet ef database update PreviousMigrationName --project src/AMS.Infrastructure --startup-project src/AMS.API
```

### Migration Scripts

```bash
# Generate SQL script for migrations
dotnet ef migrations script --project src/AMS.Infrastructure --startup-project src/AMS.API --output migrations.sql

# Generate script from specific migration
dotnet ef migrations script FromMigration ToMigration --project src/AMS.Infrastructure --startup-project src/AMS.API
```

## Database Schema

### Users Table

The application creates a `Users` table with the following structure:

```sql
CREATE TABLE "Users" (
    "Id" uuid NOT NULL,
    "FirstName" character varying(50) NOT NULL,
    "LastName" character varying(50) NOT NULL,
    "Email" character varying(100) NOT NULL,
    "PasswordHash" character varying(500) NOT NULL,
    "PhoneNumber" character varying(20) NOT NULL,
    "Department" character varying(100) NOT NULL,
    "JobTitle" character varying(100) NOT NULL,
    "IsActive" boolean NOT NULL DEFAULT true,
    "LastLoginAt" timestamp with time zone,
    "Role" character varying(50) NOT NULL DEFAULT 'User',
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100),
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL DEFAULT false,
    CONSTRAINT "PK_Users" PRIMARY KEY ("Id")
);

-- Indexes
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
CREATE INDEX "IX_Users_Department" ON "Users" ("Department");
CREATE INDEX "IX_Users_IsActive" ON "Users" ("IsActive");
CREATE INDEX "IX_Users_IsDeleted" ON "Users" ("IsDeleted");
CREATE INDEX "IX_Users_Role" ON "Users" ("Role");
```

## Initial Data Seeding

The application automatically seeds initial users:

1. **Administrator**: <EMAIL> / Admin123!
2. **Manager**: <EMAIL> / Manager123!
3. **User**: <EMAIL> / User123!

### Manual Seeding

You can also seed data manually:

```sql
INSERT INTO "Users" ("Id", "FirstName", "LastName", "Email", "PasswordHash", "Role", "IsActive", "CreatedAt", "CreatedBy")
VALUES
(gen_random_uuid(), 'Admin', 'User', '<EMAIL>', 'hashed_password', 'Administrator', true, NOW(), 'System');
```

## Database Operations

### Backup

```bash
# Create backup
pg_dump -h localhost -U ams_user -d ams_development > backup.sql

# Create compressed backup
pg_dump -h localhost -U ams_user -d ams_development | gzip > backup.sql.gz
```

### Restore

```bash
# Restore from backup
psql -h localhost -U ams_user -d ams_development < backup.sql

# Restore from compressed backup
gunzip -c backup.sql.gz | psql -h localhost -U ams_user -d ams_development
```

### Reset Database (Development)

```bash
# Drop and recreate database
dotnet ef database drop --project src/AMS.Infrastructure --startup-project src/AMS.API --force
dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API
```

## Performance Optimization

### Connection Pooling

The application uses Entity Framework's built-in connection pooling. Configure in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ams_development;Username=ams_user;Password=password;Pooling=true;MinPoolSize=5;MaxPoolSize=100"
  }
}
```

### Indexes

The application creates indexes for:

- Email (unique)
- Department
- IsActive
- IsDeleted
- Role

### Query Optimization

- Soft delete queries are automatically filtered
- Audit fields are automatically populated
- Use async methods for all database operations

## Monitoring and Maintenance

### Health Checks

The application includes database health checks:

- Connection testing
- Query execution validation
- Migration status verification

### Logging

Database operations are logged with different levels:

- Information: Successful operations
- Warning: Performance issues
- Error: Connection failures, query errors

### Maintenance Tasks

Regular maintenance should include:

- Database backups
- Index maintenance
- Statistics updates
- Log cleanup

## Troubleshooting

### Common Issues

#### Connection Refused

```
Npgsql.NpgsqlException: Connection refused
```

**Solution**: Ensure PostgreSQL is running and accepting connections.

#### Authentication Failed

```
Npgsql.NpgsqlException: password authentication failed
```

**Solution**: Verify username/password in connection string.

#### Database Does Not Exist

```
Npgsql.NpgsqlException: database "ams_development" does not exist
```

**Solution**: Create database or run migrations.

#### Permission Denied

```
Npgsql.NpgsqlException: permission denied for table Users
```

**Solution**: Grant proper permissions to database user.

### Debug Commands

```bash
# Test connection
dotnet ef dbcontext info --project src/AMS.Infrastructure --startup-project src/AMS.API

# List migrations
dotnet ef migrations list --project src/AMS.Infrastructure --startup-project src/AMS.API

# Check pending migrations
dotnet ef migrations has-pending-model-changes --project src/AMS.Infrastructure --startup-project src/AMS.API
```
