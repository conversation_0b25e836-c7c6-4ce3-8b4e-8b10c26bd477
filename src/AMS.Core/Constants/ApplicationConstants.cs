namespace AMS.Core.Constants;

/// <summary>
/// Application-wide constants
/// </summary>
public static class ApplicationConstants
{
    public static class Roles
    {
        public const string Administrator = "Administrator";
        public const string User = "User";
        public const string Manager = "Manager";
    }

    public static class Policies
    {
        public const string RequireAdministratorRole = "RequireAdministratorRole";
        public const string RequireUserRole = "RequireUserRole";
        public const string RequireManagerRole = "RequireManagerRole";
    }

    public static class ClaimTypes
    {
        public const string UserId = "user_id";
        public const string Email = "email";
        public const string Role = "role";
        public const string FirstName = "first_name";
        public const string LastName = "last_name";
    }

    public static class ConnectionStrings
    {
        public const string DefaultConnection = "DefaultConnection";
    }

    public static class Authentication
    {
        public const int TokenExpirationMinutes = 60;
        public const int RefreshTokenExpirationDays = 7;
        public const int PasswordResetTokenExpirationHours = 24;
        public const int MinPasswordLength = 8;
        public const int MaxLoginAttempts = 5;
        public const int LockoutDurationMinutes = 15;
    }

    public static class Validation
    {
        public const int MaxFirstNameLength = 50;
        public const int MaxLastNameLength = 50;
        public const int MaxEmailLength = 100;
        public const int MaxSupportQuestionNameLength = 100;
        public const int MaxSupportQuestionBodyLength = 2000;
    }

    public static class SupportQuestionStatus
    {
        public const string Pending = "Pending";
        public const string InProgress = "InProgress";
        public const string Resolved = "Resolved";
        public const string Closed = "Closed";
    }
}
