namespace AMS.Core.Entities;

/// <summary>
/// Represents a user in the system
/// </summary>
public class User : BaseEntity
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime? LastLoginAt { get; set; }
    public string Role { get; set; } = "User";

    // Computed properties
    public string FullName => $"{FirstName} {LastName}".Trim();
    public bool IsAdministrator => Role == "Administrator";
    public bool IsManager => Role == "Manager" || IsAdministrator;
}
