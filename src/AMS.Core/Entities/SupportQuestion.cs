namespace AMS.Core.Entities;

/// <summary>
/// Represents a support question submitted by users
/// </summary>
public class SupportQuestion : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string ProcessingStatus { get; set; } = Constants.ApplicationConstants.SupportQuestionStatus.Pending;

    // Navigation properties
    public Guid? AssignedToUserId { get; set; }
    public User? AssignedToUser { get; set; }

    // Computed properties
    public bool IsPending => ProcessingStatus == Constants.ApplicationConstants.SupportQuestionStatus.Pending;
    public bool IsInProgress => ProcessingStatus == Constants.ApplicationConstants.SupportQuestionStatus.InProgress;
    public bool IsResolved => ProcessingStatus == Constants.ApplicationConstants.SupportQuestionStatus.Resolved;
    public bool IsClosed => ProcessingStatus == Constants.ApplicationConstants.SupportQuestionStatus.Closed;
}
