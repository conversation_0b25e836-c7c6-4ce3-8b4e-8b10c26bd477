namespace AMS.Core.Interfaces;

/// <summary>
/// Service interface for password operations
/// </summary>
public interface IPasswordService
{
    string HashPassword(string password);
    bool VerifyPassword(string password, string hashedPassword);
    string GenerateRandomPassword(int length = 12);
    bool IsPasswordStrong(string password);
    string GeneratePasswordResetToken();
    bool ValidatePasswordResetToken(string token, DateTime createdAt, int validityHours = 24);
}
