using AMS.Core.Entities;

namespace AMS.Core.Interfaces;

/// <summary>
/// Repository interface for SupportQuestion entity
/// </summary>
public interface ISupportQuestionRepository : IRepository<SupportQuestion>
{
    /// <summary>
    /// Get support questions by processing status
    /// </summary>
    /// <param name="status">Processing status to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions with the specified status</returns>
    Task<IEnumerable<SupportQuestion>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions assigned to a specific user
    /// </summary>
    /// <param name="userId">User ID to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions assigned to the user</returns>
    Task<IEnumerable<SupportQuestion>> GetByAssignedUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions by email address
    /// </summary>
    /// <param name="email">Email address to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions from the specified email</returns>
    Task<IEnumerable<SupportQuestion>> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions with pagination and filtering
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="assignedToUserId">Optional assigned user filter</param>
    /// <param name="email">Optional email filter</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated collection of support questions</returns>
    Task<IEnumerable<SupportQuestion>> GetPagedAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get count of support questions with filtering
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="assignedToUserId">Optional assigned user filter</param>
    /// <param name="email">Optional email filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Count of support questions matching the filters</returns>
    Task<int> GetCountAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        CancellationToken cancellationToken = default);
}
