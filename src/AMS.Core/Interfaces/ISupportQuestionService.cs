using AMS.Core.Entities;

namespace AMS.Core.Interfaces;

/// <summary>
/// Service interface for SupportQuestion business logic
/// </summary>
public interface ISupportQuestionService
{
    /// <summary>
    /// Get all support questions with optional filtering and pagination
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="assignedToUserId">Optional assigned user filter</param>
    /// <param name="email">Optional email filter</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions</returns>
    Task<IEnumerable<SupportQuestion>> GetAllAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get count of support questions with optional filtering
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="assignedToUserId">Optional assigned user filter</param>
    /// <param name="email">Optional email filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Count of support questions</returns>
    Task<int> GetCountAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support question by ID
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Support question if found, null otherwise</returns>
    Task<SupportQuestion?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a new support question
    /// </summary>
    /// <param name="name">Name of the person submitting the question</param>
    /// <param name="email">Email address of the person submitting the question</param>
    /// <param name="body">Body/content of the support question</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created support question</returns>
    Task<SupportQuestion> CreateAsync(string name, string email, string body, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update support question processing status
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="status">New processing status</param>
    /// <param name="updatedBy">User making the update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated support question</returns>
    Task<SupportQuestion> UpdateStatusAsync(Guid id, string status, string updatedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Assign support question to a user
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="assignedToUserId">User ID to assign to</param>
    /// <param name="updatedBy">User making the assignment</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated support question</returns>
    Task<SupportQuestion> AssignToUserAsync(Guid id, Guid? assignedToUserId, string updatedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete support question (soft delete)
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="deletedBy">User performing the deletion</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions by status
    /// </summary>
    /// <param name="status">Processing status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions with the specified status</returns>
    Task<IEnumerable<SupportQuestion>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions assigned to a specific user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions assigned to the user</returns>
    Task<IEnumerable<SupportQuestion>> GetByAssignedUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get support questions by email address
    /// </summary>
    /// <param name="email">Email address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of support questions from the specified email</returns>
    Task<IEnumerable<SupportQuestion>> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
}
