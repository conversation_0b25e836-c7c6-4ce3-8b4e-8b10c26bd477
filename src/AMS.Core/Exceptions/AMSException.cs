namespace AMS.Core.Exceptions;

/// <summary>
/// Base exception class for AMS application-specific exceptions
/// </summary>
public abstract class AMSException : Exception
{
    protected AMSException(string message) : base(message) { }
    protected AMSException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when a requested entity is not found
/// </summary>
public class EntityNotFoundException : AMSException
{
    public EntityNotFoundException(string entityName, object key)
        : base($"Entity '{entityName}' with key '{key}' was not found.") { }
}

/// <summary>
/// Exception thrown when a business rule validation fails
/// </summary>
public class BusinessRuleValidationException : AMSException
{
    public BusinessRuleValidationException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when an unauthorized access attempt is made
/// </summary>
public class AMSUnauthorizedAccessException : AMSException
{
    public AMSUnauthorizedAccessException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when user authentication fails
/// </summary>
public class AuthenticationFailedException : AMSException
{
    public AuthenticationFailedException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when a user account is locked
/// </summary>
public class AccountLockedException : AMSException
{
    public AccountLockedException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when a duplicate email is detected
/// </summary>
public class DuplicateEmailException : AMSException
{
    public DuplicateEmailException(string email) : base($"Email '{email}' is already in use.") { }
}

/// <summary>
/// Exception thrown when password validation fails
/// </summary>
public class InvalidPasswordException : AMSException
{
    public InvalidPasswordException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when validation fails
/// </summary>
public class ValidationException : AMSException
{
    public ValidationException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when a requested entity is not found
/// </summary>
public class NotFoundException : AMSException
{
    public NotFoundException(string message) : base(message) { }
}
