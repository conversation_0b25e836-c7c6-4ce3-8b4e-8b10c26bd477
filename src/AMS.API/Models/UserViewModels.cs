using System.ComponentModel.DataAnnotations;
using AMS.Core.Entities;
using AMS.Core.Constants;

namespace AMS.API.Models;

/// <summary>
/// View model for user list
/// </summary>
public class UserListViewModel
{
    public List<User> Users { get; set; } = new();
    public string? SearchTerm { get; set; }
    public string? RoleFilter { get; set; }
    public bool? IsActiveFilter { get; set; }
}

/// <summary>
/// View model for creating users
/// </summary>
public class CreateUserViewModel
{
    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [StringLength(100, ErrorMessage = "Password must be at least {2} characters long.", MinimumLength = 8)]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public string Password { get; set; } = string.Empty;

    [DataType(DataType.Password)]
    [Display(Name = "Confirm Password")]
    [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
    public string ConfirmPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role is required")]
    [Display(Name = "Role")]
    public string Role { get; set; } = ApplicationConstants.Roles.User;

    [Display(Name = "Active")]
    public bool IsActive { get; set; } = true;

    public static List<string> AvailableRoles => new()
    {
        ApplicationConstants.Roles.User,
        ApplicationConstants.Roles.Manager,
        ApplicationConstants.Roles.Administrator
    };
}

/// <summary>
/// View model for editing users
/// </summary>
public class EditUserViewModel
{
    public Guid Id { get; set; }

    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role is required")]
    [Display(Name = "Role")]
    public string Role { get; set; } = ApplicationConstants.Roles.User;

    [Display(Name = "Active")]
    public bool IsActive { get; set; } = true;

    public static List<string> AvailableRoles => new()
    {
        ApplicationConstants.Roles.User,
        ApplicationConstants.Roles.Manager,
        ApplicationConstants.Roles.Administrator
    };
}
