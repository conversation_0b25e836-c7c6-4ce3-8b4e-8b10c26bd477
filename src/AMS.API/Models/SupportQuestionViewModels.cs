using System.ComponentModel.DataAnnotations;
using AMS.Core.Entities;

namespace AMS.API.Models;

/// <summary>
/// View model for creating support questions
/// </summary>
public class CreateSupportQuestionViewModel
{
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    [Display(Name = "Your Name")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Question is required")]
    [StringLength(2000, ErrorMessage = "Question cannot exceed 2000 characters", MinimumLength = 10)]
    [Display(Name = "Your Question")]
    public string Body { get; set; } = string.Empty;
}

/// <summary>
/// View model for support question list
/// </summary>
public class SupportQuestionListViewModel
{
    public List<SupportQuestion> SupportQuestions { get; set; } = new();
    public int CurrentPage { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
    public string? StatusFilter { get; set; }
    public string? EmailFilter { get; set; }
}

/// <summary>
/// View model for support question details
/// </summary>
public class SupportQuestionDetailsViewModel
{
    public SupportQuestion SupportQuestion { get; set; } = null!;
    public List<User> AvailableUsers { get; set; } = new();
}

/// <summary>
/// View model for updating support question status
/// </summary>
public class UpdateSupportQuestionStatusViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Status is required")]
    [Display(Name = "Status")]
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// View model for assigning support question
/// </summary>
public class AssignSupportQuestionViewModel
{
    public Guid Id { get; set; }
    
    [Display(Name = "Assign To")]
    public Guid? AssignedToUserId { get; set; }
}
