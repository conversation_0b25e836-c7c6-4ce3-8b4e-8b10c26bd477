using System.ComponentModel.DataAnnotations;
using AMS.Core.Constants;

namespace AMS.API.DTOs;

/// <summary>
/// DTO for user login request
/// </summary>
public class LoginRequestDto
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// DTO for user login response
/// </summary>
public class LoginResponseDto
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public UserDto User { get; set; } = null!;
}

/// <summary>
/// DTO for user information
/// </summary>
public class UserDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public string Role { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string FullName { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating a new user
/// </summary>
public class CreateUserDto
{
    [Required(ErrorMessage = "First name is required")]
    [StringLength(ApplicationConstants.Validation.MaxFirstNameLength, ErrorMessage = "First name is too long")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [StringLength(ApplicationConstants.Validation.MaxLastNameLength, ErrorMessage = "Last name is too long")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(ApplicationConstants.Validation.MaxEmailLength, ErrorMessage = "Email is too long")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [MinLength(ApplicationConstants.Authentication.MinPasswordLength, ErrorMessage = "Password is too short")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role is required")]
    public string Role { get; set; } = ApplicationConstants.Roles.User;
}

/// <summary>
/// DTO for updating user information
/// </summary>
public class UpdateUserDto
{
    [Required(ErrorMessage = "First name is required")]
    [StringLength(ApplicationConstants.Validation.MaxFirstNameLength, ErrorMessage = "First name is too long")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [StringLength(ApplicationConstants.Validation.MaxLastNameLength, ErrorMessage = "Last name is too long")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(ApplicationConstants.Validation.MaxEmailLength, ErrorMessage = "Email is too long")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Role is required")]
    public string Role { get; set; } = ApplicationConstants.Roles.User;

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for changing password
/// </summary>
public class ChangePasswordDto
{
    [Required(ErrorMessage = "Current password is required")]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "New password is required")]
    [MinLength(ApplicationConstants.Authentication.MinPasswordLength, ErrorMessage = "Password is too short")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Confirm password is required")]
    [Compare(nameof(NewPassword), ErrorMessage = "Passwords do not match")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

/// <summary>
/// DTO for password reset
/// </summary>
public class ResetPasswordDto
{
    [Required(ErrorMessage = "New password is required")]
    [MinLength(ApplicationConstants.Authentication.MinPasswordLength, ErrorMessage = "Password is too short")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Confirm password is required")]
    [Compare(nameof(NewPassword), ErrorMessage = "Passwords do not match")]
    public string ConfirmPassword { get; set; } = string.Empty;
}
