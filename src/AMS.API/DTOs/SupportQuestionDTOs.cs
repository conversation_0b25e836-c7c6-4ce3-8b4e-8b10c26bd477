namespace AMS.API.DTOs;

/// <summary>
/// DTO for creating a new support question
/// </summary>
public class CreateSupportQuestionDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating support question status
/// </summary>
public class UpdateSupportQuestionStatusDto
{
    public string ProcessingStatus { get; set; } = string.Empty;
}

/// <summary>
/// DTO for assigning support question to a user
/// </summary>
public class AssignSupportQuestionDto
{
    public Guid? AssignedToUserId { get; set; }
}

/// <summary>
/// DTO for support question information
/// </summary>
public class SupportQuestionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string ProcessingStatus { get; set; } = string.Empty;
    public Guid? AssignedToUserId { get; set; }
    public UserDto? AssignedToUser { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }

    // Computed properties
    public bool IsPending { get; set; }
    public bool IsInProgress { get; set; }
    public bool IsResolved { get; set; }
    public bool IsClosed { get; set; }
}

/// <summary>
/// DTO for paginated support question results
/// </summary>
public class PagedSupportQuestionDto
{
    public IEnumerable<SupportQuestionDto> Items { get; set; } = new List<SupportQuestionDto>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

/// <summary>
/// DTO for support question filtering and pagination
/// </summary>
public class SupportQuestionFilterDto
{
    public string? Status { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public string? Email { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}
