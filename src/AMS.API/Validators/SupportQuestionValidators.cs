using AMS.API.DTOs;
using AMS.Core.Constants;
using FluentValidation;

namespace AMS.API.Validators;

/// <summary>
/// Validator for CreateSupportQuestionDto
/// </summary>
public class CreateSupportQuestionDtoValidator : AbstractValidator<CreateSupportQuestionDto>
{
    public CreateSupportQuestionDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required")
            .MaximumLength(ApplicationConstants.Validation.MaxSupportQuestionNameLength)
            .WithMessage($"Name cannot exceed {ApplicationConstants.Validation.MaxSupportQuestionNameLength} characters");

        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required")
            .EmailAddress()
            .WithMessage("Email must be a valid email address")
            .MaximumLength(ApplicationConstants.Validation.MaxEmailLength)
            .WithMessage($"Email cannot exceed {ApplicationConstants.Validation.MaxEmailLength} characters");

        RuleFor(x => x.Body)
            .NotEmpty()
            .WithMessage("Body is required")
            .MaximumLength(ApplicationConstants.Validation.MaxSupportQuestionBodyLength)
            .WithMessage($"Body cannot exceed {ApplicationConstants.Validation.MaxSupportQuestionBodyLength} characters");
    }
}

/// <summary>
/// Validator for UpdateSupportQuestionStatusDto
/// </summary>
public class UpdateSupportQuestionStatusDtoValidator : AbstractValidator<UpdateSupportQuestionStatusDto>
{
    public UpdateSupportQuestionStatusDtoValidator()
    {
        RuleFor(x => x.ProcessingStatus)
            .NotEmpty()
            .WithMessage("Processing status is required")
            .Must(BeValidStatus)
            .WithMessage("Processing status must be one of: Pending, InProgress, Resolved, Closed");
    }

    private static bool BeValidStatus(string status)
    {
        return status == ApplicationConstants.SupportQuestionStatus.Pending ||
               status == ApplicationConstants.SupportQuestionStatus.InProgress ||
               status == ApplicationConstants.SupportQuestionStatus.Resolved ||
               status == ApplicationConstants.SupportQuestionStatus.Closed;
    }
}

/// <summary>
/// Validator for AssignSupportQuestionDto
/// </summary>
public class AssignSupportQuestionDtoValidator : AbstractValidator<AssignSupportQuestionDto>
{
    public AssignSupportQuestionDtoValidator()
    {
        // AssignedToUserId can be null (to unassign) or a valid GUID
        RuleFor(x => x.AssignedToUserId)
            .Must(BeValidGuidOrNull)
            .WithMessage("AssignedToUserId must be a valid GUID or null");
    }

    private static bool BeValidGuidOrNull(Guid? guid)
    {
        return !guid.HasValue || guid.Value != Guid.Empty;
    }
}

/// <summary>
/// Validator for SupportQuestionFilterDto
/// </summary>
public class SupportQuestionFilterDtoValidator : AbstractValidator<SupportQuestionFilterDto>
{
    public SupportQuestionFilterDtoValidator()
    {
        RuleFor(x => x.Status)
            .Must(BeValidStatusOrNull)
            .WithMessage("Status must be one of: Pending, InProgress, Resolved, Closed")
            .When(x => !string.IsNullOrEmpty(x.Status));

        RuleFor(x => x.AssignedToUserId)
            .Must(BeValidGuidOrNull)
            .WithMessage("AssignedToUserId must be a valid GUID")
            .When(x => x.AssignedToUserId.HasValue);

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("Email must be a valid email address")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.Page)
            .GreaterThan(0)
            .WithMessage("Page must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("PageSize must be greater than 0")
            .LessThanOrEqualTo(100)
            .WithMessage("PageSize cannot exceed 100");
    }

    private static bool BeValidStatusOrNull(string? status)
    {
        if (string.IsNullOrEmpty(status))
            return true;

        return status == ApplicationConstants.SupportQuestionStatus.Pending ||
               status == ApplicationConstants.SupportQuestionStatus.InProgress ||
               status == ApplicationConstants.SupportQuestionStatus.Resolved ||
               status == ApplicationConstants.SupportQuestionStatus.Closed;
    }

    private static bool BeValidGuidOrNull(Guid? guid)
    {
        return !guid.HasValue || guid.Value != Guid.Empty;
    }
}
