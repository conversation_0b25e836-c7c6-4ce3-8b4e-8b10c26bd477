using FluentValidation;
using AMS.API.DTOs;
using AMS.Core.Constants;

namespace AMS.API.Validators;

/// <summary>
/// Validator for LoginRequestDto
/// </summary>
public class LoginRequestValidator : AbstractValidator<LoginRequestDto>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(ApplicationConstants.Validation.MaxEmailLength)
            .WithMessage($"Email must not exceed {ApplicationConstants.Validation.MaxEmailLength} characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(ApplicationConstants.Authentication.MinPasswordLength)
            .WithMessage($"Password must be at least {ApplicationConstants.Authentication.MinPasswordLength} characters long");
    }
}

/// <summary>
/// Validator for CreateUserDto
/// </summary>
public class CreateUserValidator : AbstractValidator<CreateUserDto>
{
    public CreateUserValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(ApplicationConstants.Validation.MaxFirstNameLength)
            .WithMessage($"First name must not exceed {ApplicationConstants.Validation.MaxFirstNameLength} characters")
            .Matches(@"^[a-zA-Z\s'-]+$").WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(ApplicationConstants.Validation.MaxLastNameLength)
            .WithMessage($"Last name must not exceed {ApplicationConstants.Validation.MaxLastNameLength} characters")
            .Matches(@"^[a-zA-Z\s'-]+$").WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(ApplicationConstants.Validation.MaxEmailLength)
            .WithMessage($"Email must not exceed {ApplicationConstants.Validation.MaxEmailLength} characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(ApplicationConstants.Authentication.MinPasswordLength)
            .WithMessage($"Password must be at least {ApplicationConstants.Authentication.MinPasswordLength} characters long")
            .Must(BeStrongPassword).WithMessage("Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character");

        RuleFor(x => x.Role)
            .NotEmpty().WithMessage("Role is required")
            .Must(BeValidRole).WithMessage("Invalid role specified");
    }

    private static bool BeStrongPassword(string password)
    {
        if (string.IsNullOrEmpty(password)) return false;

        var hasUpperCase = password.Any(char.IsUpper);
        var hasLowerCase = password.Any(char.IsLower);
        var hasDigit = password.Any(char.IsDigit);
        var hasSpecialChar = password.Any(ch => !char.IsLetterOrDigit(ch));

        return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
    }

    private static bool BeValidRole(string role)
    {
        var validRoles = new[]
        {
            ApplicationConstants.Roles.Administrator,
            ApplicationConstants.Roles.Manager,
            ApplicationConstants.Roles.User
        };

        return validRoles.Contains(role);
    }
}

/// <summary>
/// Validator for UpdateUserDto
/// </summary>
public class UpdateUserValidator : AbstractValidator<UpdateUserDto>
{
    public UpdateUserValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(ApplicationConstants.Validation.MaxFirstNameLength)
            .WithMessage($"First name must not exceed {ApplicationConstants.Validation.MaxFirstNameLength} characters")
            .Matches(@"^[a-zA-Z\s'-]+$").WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(ApplicationConstants.Validation.MaxLastNameLength)
            .WithMessage($"Last name must not exceed {ApplicationConstants.Validation.MaxLastNameLength} characters")
            .Matches(@"^[a-zA-Z\s'-]+$").WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(ApplicationConstants.Validation.MaxEmailLength)
            .WithMessage($"Email must not exceed {ApplicationConstants.Validation.MaxEmailLength} characters");

        RuleFor(x => x.Role)
            .NotEmpty().WithMessage("Role is required")
            .Must(BeValidRole).WithMessage("Invalid role specified");
    }

    private static bool BeValidRole(string role)
    {
        var validRoles = new[]
        {
            ApplicationConstants.Roles.Administrator,
            ApplicationConstants.Roles.Manager,
            ApplicationConstants.Roles.User
        };

        return validRoles.Contains(role);
    }
}

/// <summary>
/// Validator for ChangePasswordDto
/// </summary>
public class ChangePasswordValidator : AbstractValidator<ChangePasswordDto>
{
    public ChangePasswordValidator()
    {
        RuleFor(x => x.CurrentPassword)
            .NotEmpty().WithMessage("Current password is required");

        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("New password is required")
            .MinimumLength(ApplicationConstants.Authentication.MinPasswordLength)
            .WithMessage($"Password must be at least {ApplicationConstants.Authentication.MinPasswordLength} characters long")
            .Must(BeStrongPassword).WithMessage("Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character");

        RuleFor(x => x.ConfirmPassword)
            .NotEmpty().WithMessage("Confirm password is required")
            .Equal(x => x.NewPassword).WithMessage("Passwords do not match");
    }

    private static bool BeStrongPassword(string password)
    {
        if (string.IsNullOrEmpty(password)) return false;

        var hasUpperCase = password.Any(char.IsUpper);
        var hasLowerCase = password.Any(char.IsLower);
        var hasDigit = password.Any(char.IsDigit);
        var hasSpecialChar = password.Any(ch => !char.IsLetterOrDigit(ch));

        return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
    }
}

/// <summary>
/// Validator for ResetPasswordDto
/// </summary>
public class ResetPasswordValidator : AbstractValidator<ResetPasswordDto>
{
    public ResetPasswordValidator()
    {
        RuleFor(x => x.NewPassword)
            .NotEmpty().WithMessage("New password is required")
            .MinimumLength(ApplicationConstants.Authentication.MinPasswordLength)
            .WithMessage($"Password must be at least {ApplicationConstants.Authentication.MinPasswordLength} characters long")
            .Must(BeStrongPassword).WithMessage("Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character");

        RuleFor(x => x.ConfirmPassword)
            .NotEmpty().WithMessage("Confirm password is required")
            .Equal(x => x.NewPassword).WithMessage("Passwords do not match");
    }

    private static bool BeStrongPassword(string password)
    {
        if (string.IsNullOrEmpty(password)) return false;

        var hasUpperCase = password.Any(char.IsUpper);
        var hasLowerCase = password.Any(char.IsLower);
        var hasDigit = password.Any(char.IsDigit);
        var hasSpecialChar = password.Any(ch => !char.IsLetterOrDigit(ch));

        return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
    }
}
