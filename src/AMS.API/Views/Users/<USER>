@model EditUserViewModel
@{
    ViewData["Title"] = "Edit User";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Edit User</h1>
        <p class="text-muted mb-0">Update user information and settings</p>
    </div>
    <div>
        <a href="/users" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form asp-action="Edit" method="post" data-loading="true">
                    <input asp-for="Id" type="hidden" />
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="FirstName" class="form-control" placeholder="First Name" />
                                <label asp-for="FirstName"></label>
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="LastName" class="form-control" placeholder="Last Name" />
                                <label asp-for="LastName"></label>
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-floating">
                                <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                <label asp-for="Email"></label>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select asp-for="Role" class="form-select">
                                    @foreach (var role in EditUserViewModel.AvailableRoles)
                                    {
                                        <option value="@role" selected="@(Model.Role == role)">@role</option>
                                    }
                                </select>
                                <label asp-for="Role"></label>
                                <span asp-validation-for="Role" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 d-flex align-items-center">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">
                                    User is active
                                </label>
                                <span asp-validation-for="IsActive" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Role Information -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>
                            Role Permissions
                        </h6>
                        <ul class="mb-0">
                            <li><strong>Administrator:</strong> Full system access including user management</li>
                            <li><strong>Manager:</strong> Can manage support questions and view reports</li>
                            <li><strong>User:</strong> Basic access to submit and view own support questions</li>
                        </ul>
                    </div>

                    @if (User.FindFirst("user_id")?.Value == Model.Id.ToString())
                    {
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> You are editing your own account. Be careful not to remove your own administrator privileges.
                        </div>
                    }

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/users/details/@Model.Id" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    Security Information
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Password changes must be handled separately for security reasons</li>
                    <li>Email changes will require the user to verify their new email address</li>
                    <li>Role changes take effect immediately upon saving</li>
                    <li>Deactivating a user will prevent them from logging in</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
