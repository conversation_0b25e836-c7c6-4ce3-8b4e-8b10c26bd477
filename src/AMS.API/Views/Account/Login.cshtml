@model LoginViewModel
@{
    ViewData["Title"] = "Sign In";
    Layout = "_Layout";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="bi bi-building text-primary" style="font-size: 3rem;"></i>
                        <h2 class="h4 mt-2 mb-0">Welcome Back</h2>
                        <p class="text-muted">Sign in to your AMS account</p>
                    </div>

                    <form asp-action="Login" asp-route-returnUrl="@ViewData["ReturnUrl"]" method="post" data-loading="true">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="form-floating mb-3">
                            <input asp-for="Email" class="form-control" placeholder="<EMAIL>" autocomplete="email" />
                            <label asp-for="Email"></label>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-floating mb-3">
                            <input asp-for="Password" class="form-control" placeholder="Password" autocomplete="current-password" />
                            <label asp-for="Password"></label>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Remember me on this device
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                Sign In
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="text-muted mb-2">Need help accessing your account?</p>
                        <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-question-circle me-1"></i>
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Demo Accounts Info -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info bg-opacity-10">
                    <h6 class="card-title mb-0 text-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Demo Accounts
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text small text-muted mb-3">
                        Use these pre-configured accounts for testing:
                    </p>
                    <div class="row g-2">
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-danger">Administrator</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: Admin123!
                                </small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-warning">Manager</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: Manager123!
                                </small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-info">User</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: User123!
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Auto-fill demo credentials
        document.addEventListener('DOMContentLoaded', function() {
            const demoAccounts = document.querySelectorAll('.demo-account');
            demoAccounts.forEach(account => {
                account.style.cursor = 'pointer';
                account.addEventListener('click', function() {
                    const text = this.textContent;
                    const emailMatch = text.match(/Email: ([^\s]+)/);
                    const passwordMatch = text.match(/Password: ([^\s]+)/);
                    
                    if (emailMatch && passwordMatch) {
                        document.getElementById('Email').value = emailMatch[1];
                        document.getElementById('Password').value = passwordMatch[1];
                        
                        // Show feedback
                        AMS.utils.showToast('Demo credentials filled in. Click Sign In to continue.', 'info');
                    }
                });
            });
        });
    </script>
}

<style>
.demo-account {
    transition: background-color 0.2s;
}

.demo-account:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .65;
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}
</style>
