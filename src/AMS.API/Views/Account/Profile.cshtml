@model ProfileViewModel
@{
    ViewData["Title"] = "My Profile";
}

<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">My Profile</h1>
                    <p class="text-muted mb-0">Manage your account information</p>
                </div>
                <div>
                    <span class="badge bg-@(Model.IsActive ? "success" : "secondary") fs-6">
                        @(Model.IsActive ? "Active" : "Inactive")
                    </span>
                </div>
            </div>

            <div class="row g-4">
                <!-- Profile Information -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person me-2"></i>
                                Profile Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <label class="form-label text-muted">First Name</label>
                                    <p class="form-control-plaintext">@Model.FirstName</p>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label text-muted">Last Name</label>
                                    <p class="form-control-plaintext">@Model.LastName</p>
                                </div>
                                <div class="col-12">
                                    <label class="form-label text-muted">Email Address</label>
                                    <p class="form-control-plaintext">
                                        <i class="bi bi-envelope me-2"></i>
                                        @Model.Email
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label text-muted">Role</label>
                                    <p class="form-control-plaintext">
                                        <span class="badge bg-primary">@Model.Role</span>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label text-muted">Status</label>
                                    <p class="form-control-plaintext">
                                        <span class="badge bg-@(Model.IsActive ? "success" : "secondary")">
                                            @(Model.IsActive ? "Active" : "Inactive")
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Statistics -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-graph-up me-2"></i>
                                Account Info
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-calendar-plus text-primary fs-4"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Member Since</h6>
                                    <small class="text-muted">@Model.CreatedAt.ToString("MMMM dd, yyyy")</small>
                                </div>
                            </div>
                            
                            @if (Model.LastLoginAt.HasValue)
                            {
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-clock-history text-success fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-0">Last Login</h6>
                                        <small class="text-muted">@Model.LastLoginAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-clock text-muted fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-0">Last Login</h6>
                                        <small class="text-muted">Never</small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-question-circle me-2"></i>
                                    Submit Support Question
                                </a>
                                <a asp-controller="Home" asp-action="Dashboard" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-speedometer2 me-2"></i>
                                    Go to Dashboard
                                </a>
                                @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
                                {
                                    <a asp-controller="SupportQuestions" asp-action="Index" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-chat-dots me-2"></i>
                                        Manage Support Questions
                                    </a>
                                }
                                @if (User.IsInRole("Administrator"))
                                {
                                    <a asp-controller="Users" asp-action="Index" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-people me-2"></i>
                                        Manage Users
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-shield-check me-2"></i>
                                Security & Privacy
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-key text-warning me-3 fs-5"></i>
                                        <div>
                                            <h6 class="mb-1">Password</h6>
                                            <small class="text-muted">Last changed: Not available</small>
                                        </div>
                                        <div class="ms-auto">
                                            <button class="btn btn-outline-primary btn-sm" disabled>
                                                Change Password
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-shield-lock text-success me-3 fs-5"></i>
                                        <div>
                                            <h6 class="mb-1">Two-Factor Authentication</h6>
                                            <small class="text-muted">Not configured</small>
                                        </div>
                                        <div class="ms-auto">
                                            <button class="btn btn-outline-secondary btn-sm" disabled>
                                                Setup 2FA
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-3">
                            
                            <div class="text-center">
                                <p class="text-muted mb-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    To update your profile information or change security settings, please contact your administrator.
                                </p>
                                <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-envelope me-2"></i>
                                    Contact Administrator
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
