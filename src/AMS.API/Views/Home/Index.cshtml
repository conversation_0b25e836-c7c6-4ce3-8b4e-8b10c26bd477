@{
    ViewData["Title"] = "Welcome to AMS";
}

<div class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-building me-3"></i>
                    Asset Management System
                </h1>
                <p class="lead mb-4">
                    A comprehensive, secure, and scalable solution for managing your organization's assets and support requests.
                </p>
                <div class="d-flex gap-3">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <a asp-controller="Home" asp-action="Dashboard" class="btn btn-light btn-lg">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Go to Dashboard
                        </a>
                    }
                    else
                    {
                        <a asp-controller="Account" asp-action="Login" class="btn btn-light btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            Sign In
                        </a>
                    }
                    <a href="/support/create" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-question-circle me-2"></i>
                        Submit Question
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="bi bi-diagram-3 display-1 opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto text-center mb-5">
            <h2 class="h3 mb-3">Key Features</h2>
            <p class="text-muted">
                Our AMS platform provides everything you need to efficiently manage your organization's operations.
            </p>
        </div>
    </div>

    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-people text-primary fs-4"></i>
                    </div>
                    <h5 class="card-title">User Management</h5>
                    <p class="card-text text-muted">
                        Comprehensive user management with role-based access control and secure authentication.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-chat-dots text-success fs-4"></i>
                    </div>
                    <h5 class="card-title">Support System</h5>
                    <p class="card-text text-muted">
                        Integrated support question system with status tracking and assignment capabilities.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-shield-check text-info fs-4"></i>
                    </div>
                    <h5 class="card-title">Security First</h5>
                    <p class="card-text text-muted">
                        Built with security in mind featuring JWT authentication and policy-based authorization.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-2">Need Help?</h5>
                            <p class="card-text text-muted mb-0">
                                Submit a support question and our team will get back to you as soon as possible.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/support/create" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>
                                Submit Question
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
}

.feature-icon {
    transition: transform 0.2s ease-in-out;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}
</style>
