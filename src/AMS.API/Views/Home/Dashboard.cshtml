@model DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Dashboard</h1>
        <p class="text-muted mb-0">Welcome back, @User.Identity?.Name</p>
    </div>
    <div class="text-muted">
        <i class="bi bi-clock me-1"></i>
        Last updated: @DateTime.Now.ToString("MMM dd, yyyy HH:mm")
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card card-stats">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">Total Users</h5>
                        <span class="h2 font-weight-bold mb-0">@Model.TotalUsers</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-primary">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-success me-2">
                        <i class="bi bi-person-check"></i> @Model.ActiveUsers active
                    </span>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card card-stats warning">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">Pending Questions</h5>
                        <span class="h2 font-weight-bold mb-0">@Model.PendingSupportQuestions</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-warning">
                            <i class="bi bi-clock-history"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-warning me-2">
                        <i class="bi bi-exclamation-triangle"></i> Needs attention
                    </span>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card card-stats info">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">In Progress</h5>
                        <span class="h2 font-weight-bold mb-0">@Model.InProgressSupportQuestions</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-info">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-info me-2">
                        <i class="bi bi-gear"></i> Being processed
                    </span>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card card-stats success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">Resolved</h5>
                        <span class="h2 font-weight-bold mb-0">@Model.ResolvedSupportQuestions</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-success">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-success me-2">
                        <i class="bi bi-check2"></i> Completed
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-5">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-dots me-2"></i>
                    Recent Support Questions
                </h5>
                @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
                {
                    <a asp-controller="SupportQuestions" asp-action="Index" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                }
            </div>
            <div class="card-body">
                @if (Model.RecentSupportQuestions.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var question in Model.RecentSupportQuestions)
                                {
                                    <tr>
                                        <td>@question.Name</td>
                                        <td>@question.Email</td>
                                        <td>
                                            <span class="badge status-badge <EMAIL>().Replace(" ", "-")">
                                                @question.ProcessingStatus
                                            </span>
                                        </td>
                                        <td>@question.CreatedAt.ToString("MMM dd, yyyy")</td>
                                        <td>
                                            @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
                                            {
                                                <a href="/support/details/@question.Id"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No recent support questions</p>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/support/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Submit Support Question
                    </a>

                    @if (User.IsInRole("Administrator"))
                    {
                        <a href="/users/create" class="btn btn-outline-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            Add New User
                        </a>
                        <a href="/users" class="btn btn-outline-secondary">
                            <i class="bi bi-people me-2"></i>
                            Manage Users
                        </a>
                    }

                    @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
                    {
                        <a href="/support" class="btn btn-outline-secondary">
                            <i class="bi bi-chat-dots me-2"></i>
                            Manage Support Questions
                        </a>
                    }
                    
                    <a href="/swagger" class="btn btn-outline-info" target="_blank">
                        <i class="bi bi-code-slash me-2"></i>
                        API Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
