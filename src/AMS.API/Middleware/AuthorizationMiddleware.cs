using System.Text.Json;

namespace AMS.API.Middleware;

/// <summary>
/// Middleware for handling authorization failures
/// </summary>
public class AuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuthorizationMiddleware> _logger;

    public AuthorizationMiddleware(RequestDelegate next, ILogger<AuthorizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await _next(context);

        // Handle authorization failures
        if (context.Response.StatusCode == 403)
        {
            await HandleForbiddenAsync(context);
        }
    }

    private static async Task HandleForbiddenAsync(HttpContext context)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            message = "You do not have permission to access this resource",
            statusCode = 403,
            timestamp = DateTime.UtcNow
        };

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}
