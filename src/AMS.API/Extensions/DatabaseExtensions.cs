using Microsoft.EntityFrameworkCore;
using AMS.Infrastructure.Data;

namespace AMS.API.Extensions;

/// <summary>
/// Extension methods for database initialization
/// </summary>
public static class DatabaseExtensions
{
    /// <summary>
    /// Initializes the database with migrations and seeding
    /// </summary>
    public static async Task<IApplicationBuilder> InitializeDatabaseAsync(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("Initializing database...");

            var context = services.GetRequiredService<ApplicationDbContext>();
            var seeder = services.GetRequiredService<DatabaseSeeder>();

            // Apply migrations and seed data
            await seeder.SeedAsync();

            logger.LogInformation("Database initialization completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database");
            throw;
        }

        return app;
    }

    /// <summary>
    /// Ensures the database is created and applies pending migrations
    /// </summary>
    public static async Task<IApplicationBuilder> EnsureDatabaseAsync(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("Ensuring database exists...");

            var context = services.GetRequiredService<ApplicationDbContext>();

            // Check if database exists
            var canConnect = await context.Database.CanConnectAsync();
            if (!canConnect)
            {
                logger.LogInformation("Database does not exist, creating...");
                await context.Database.EnsureCreatedAsync();
            }

            // Apply pending migrations
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                logger.LogInformation("Applying {Count} pending migrations: {Migrations}",
                    pendingMigrations.Count(),
                    string.Join(", ", pendingMigrations));

                await context.Database.MigrateAsync();
                logger.LogInformation("Migrations applied successfully");
            }
            else
            {
                logger.LogInformation("Database is up to date");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while ensuring database");
            throw;
        }

        return app;
    }

    /// <summary>
    /// Checks database health and connectivity
    /// </summary>
    public static async Task<bool> CheckDatabaseHealthAsync(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<Program>>();

        try
        {
            var context = services.GetRequiredService<ApplicationDbContext>();
            
            // Test database connectivity
            var canConnect = await context.Database.CanConnectAsync();
            if (!canConnect)
            {
                logger.LogError("Cannot connect to database");
                return false;
            }

            // Test a simple query
            var userCount = await context.Users.CountAsync();
            logger.LogInformation("Database health check passed. User count: {Count}", userCount);
            
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Database health check failed");
            return false;
        }
    }
}
