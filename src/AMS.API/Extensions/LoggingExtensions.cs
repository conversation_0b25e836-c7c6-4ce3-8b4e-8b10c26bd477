namespace AMS.API.Extensions;

/// <summary>
/// Extension methods for logging configuration
/// </summary>
public static class LoggingExtensions
{
    /// <summary>
    /// Configures structured logging
    /// </summary>
    public static IServiceCollection AddLoggingServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddConsole();
            builder.AddDebug();

            // Add file logging if configured
            var logPath = configuration["Logging:FilePath"];
            if (!string.IsNullOrEmpty(logPath))
            {
                // File logging configuration would go here
                // For example, using Serilog or NLog
            }
        });

        return services;
    }

    /// <summary>
    /// Configures request logging middleware
    /// </summary>
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder app)
    {
        app.Use(async (context, next) =>
        {
            var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
            
            var startTime = DateTime.UtcNow;
            
            logger.LogInformation("Request started: {Method} {Path} from {RemoteIpAddress}",
                context.Request.Method,
                context.Request.Path,
                context.Connection.RemoteIpAddress);

            await next();

            var duration = DateTime.UtcNow - startTime;
            
            logger.LogInformation("Request completed: {Method} {Path} responded {StatusCode} in {Duration}ms",
                context.Request.Method,
                context.Request.Path,
                context.Response.StatusCode,
                duration.TotalMilliseconds);
        });

        return app;
    }
}
