namespace AMS.API.Extensions;

/// <summary>
/// Extension methods for configuration validation
/// </summary>
public static class ConfigurationExtensions
{
    /// <summary>
    /// Validates that all required configuration settings are present
    /// </summary>
    public static void ValidateConfiguration(this IConfiguration configuration)
    {
        var errors = new List<string>();

        // Validate connection string
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            errors.Add("DefaultConnection string is missing");
        }

        // Validate JWT settings
        var jwtSettings = configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"];
        var issuer = jwtSettings["Issuer"];
        var audience = jwtSettings["Audience"];

        if (string.IsNullOrEmpty(secretKey))
        {
            errors.Add("JwtSettings:<PERSON><PERSON><PERSON> is missing");
        }
        else if (secretKey.Length < 32)
        {
            errors.Add("JwtSettings:SecretKey must be at least 32 characters long");
        }

        if (string.IsNullOrEmpty(issuer))
        {
            errors.Add("JwtSettings:Issuer is missing");
        }

        if (string.IsNullOrEmpty(audience))
        {
            errors.Add("JwtSettings:Audience is missing");
        }

        if (errors.Any())
        {
            throw new InvalidOperationException($"Configuration validation failed: {string.Join(", ", errors)}");
        }
    }

    /// <summary>
    /// Gets a required configuration value or throws an exception
    /// </summary>
    public static string GetRequiredValue(this IConfiguration configuration, string key)
    {
        var value = configuration[key];
        if (string.IsNullOrEmpty(value))
        {
            throw new InvalidOperationException($"Required configuration value '{key}' is missing");
        }
        return value;
    }

    /// <summary>
    /// Gets a required configuration section or throws an exception
    /// </summary>
    public static IConfigurationSection GetRequiredSection(this IConfiguration configuration, string key)
    {
        var section = configuration.GetSection(key);
        if (!section.Exists())
        {
            throw new InvalidOperationException($"Required configuration section '{key}' is missing");
        }
        return section;
    }
}
