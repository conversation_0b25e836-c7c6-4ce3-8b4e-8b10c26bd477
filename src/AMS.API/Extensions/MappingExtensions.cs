using AMS.Core.Entities;
using AMS.API.DTOs;

namespace AMS.API.Extensions;

/// <summary>
/// Extension methods for mapping between entities and DTOs
/// </summary>
public static class MappingExtensions
{
    /// <summary>
    /// Maps User entity to UserDto
    /// </summary>
    public static UserDto ToDto(this User user)
    {
        return new UserDto
        {
            Id = user.Id,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Email = user.Email,
            IsActive = user.IsActive,
            LastLoginAt = user.LastLoginAt,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            FullName = user.FullName
        };
    }

    /// <summary>
    /// Maps CreateUserDto to User entity
    /// </summary>
    public static User ToEntity(this CreateUserDto dto)
    {
        return new User
        {
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = dto.Email,
            PasswordHash = dto.Password, // Will be hashed in service
            Role = dto.Role,
            IsActive = true
        };
    }

    /// <summary>
    /// Maps UpdateUserDto to User entity
    /// </summary>
    public static User ToEntity(this UpdateUserDto dto, Guid userId)
    {
        return new User
        {
            Id = userId,
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = dto.Email,
            Role = dto.Role,
            IsActive = dto.IsActive
        };
    }

    /// <summary>
    /// Maps collection of User entities to UserDto collection
    /// </summary>
    public static IEnumerable<UserDto> ToDto(this IEnumerable<User> users)
    {
        return users.Select(user => user.ToDto());
    }

    /// <summary>
    /// Maps SupportQuestion entity to SupportQuestionDto
    /// </summary>
    public static SupportQuestionDto ToDto(this SupportQuestion supportQuestion)
    {
        return new SupportQuestionDto
        {
            Id = supportQuestion.Id,
            Name = supportQuestion.Name,
            Email = supportQuestion.Email,
            Body = supportQuestion.Body,
            ProcessingStatus = supportQuestion.ProcessingStatus,
            AssignedToUserId = supportQuestion.AssignedToUserId,
            AssignedToUser = supportQuestion.AssignedToUser?.ToDto(),
            CreatedAt = supportQuestion.CreatedAt,
            UpdatedAt = supportQuestion.UpdatedAt,
            CreatedBy = supportQuestion.CreatedBy,
            UpdatedBy = supportQuestion.UpdatedBy,
            IsPending = supportQuestion.IsPending,
            IsInProgress = supportQuestion.IsInProgress,
            IsResolved = supportQuestion.IsResolved,
            IsClosed = supportQuestion.IsClosed
        };
    }

    /// <summary>
    /// Maps CreateSupportQuestionDto to SupportQuestion entity
    /// </summary>
    public static SupportQuestion ToEntity(this CreateSupportQuestionDto dto)
    {
        return new SupportQuestion
        {
            Name = dto.Name,
            Email = dto.Email,
            Body = dto.Body
        };
    }

    /// <summary>
    /// Maps collection of SupportQuestion entities to SupportQuestionDto collection
    /// </summary>
    public static IEnumerable<SupportQuestionDto> ToDto(this IEnumerable<SupportQuestion> supportQuestions)
    {
        return supportQuestions.Select(sq => sq.ToDto());
    }
}
