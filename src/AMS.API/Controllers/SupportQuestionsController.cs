using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Core.Exceptions;
using AMS.API.DTOs;
using AMS.API.Models;
using AMS.API.Extensions;

namespace AMS.API.Controllers;

/// <summary>
/// Unified controller for support question management - handles both web views and API endpoints
/// </summary>
/// <remarks>
/// This controller provides comprehensive support question management functionality including:
/// - Web UI views for support question management
/// - API endpoints for programmatic access
/// - Support question CRUD operations
/// - Support question search and filtering
/// - Status management and assignment to users
///
/// Web routes: /support-questions/* (returns views)
/// API routes: /support-questions/api/* (returns JSON)
/// Public endpoints allow anonymous users to submit support questions.
/// Management endpoints require authentication and appropriate role-based permissions.
/// </remarks>
[Route("support-questions")]
public class SupportQuestionsController : Controller
{
    private readonly ISupportQuestionService _supportQuestionService;
    private readonly IUserService _userService;
    private readonly ILogger<SupportQuestionsController> _logger;

    public SupportQuestionsController(
        ISupportQuestionService supportQuestionService,
        IUserService userService,
        ILogger<SupportQuestionsController> logger)
    {
        _supportQuestionService = supportQuestionService;
        _userService = userService;
        _logger = logger;
    }

    #region Web View Actions

    /// <summary>
    /// List support questions (Admin/Manager only) - Web View
    /// </summary>
    [Authorize(Policy = "WebUser")]
    [Route("")]
    [Route("index")]
    public async Task<IActionResult> Index(int page = 1, int pageSize = 10, string? status = null, string? email = null)
    {
        try
        {
            var skip = (page - 1) * pageSize;
            var supportQuestions = await _supportQuestionService.GetAllAsync(
                status: status,
                email: email,
                skip: skip,
                take: pageSize);

            var totalCount = await _supportQuestionService.GetCountAsync(
                status: status,
                email: email);

            var model = new SupportQuestionListViewModel
            {
                SupportQuestions = supportQuestions.ToList(),
                CurrentPage = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                HasNextPage = page * pageSize < totalCount,
                HasPreviousPage = page > 1,
                StatusFilter = status,
                EmailFilter = email
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading support questions list");
            TempData["ErrorMessage"] = "Error loading support questions. Please try again.";
            return View(new SupportQuestionListViewModel());
        }
    }

    /// <summary>
    /// Support question details (Admin/Manager only) - Web View
    /// </summary>
    [Authorize(Policy = "WebUser")]
    [Route("details/{id}")]
    public async Task<IActionResult> Details(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                TempData["ErrorMessage"] = "Support question not found.";
                return RedirectToAction(nameof(Index));
            }

            var model = new SupportQuestionDetailsViewModel
            {
                SupportQuestion = supportQuestion
            };

            // Get available users for assignment
            if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
            {
                var users = await _userService.GetActiveUsersAsync();
                model.AvailableUsers = users.ToList();
            }

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading support question details for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading support question details. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Create support question form (Public) - Web View
    /// </summary>
    [AllowAnonymous]
    [Route("create")]
    public IActionResult Create()
    {
        return View(new CreateSupportQuestionViewModel());
    }

    /// <summary>
    /// Process create support question form (Public) - Web View
    /// </summary>
    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    [Route("create")]
    public async Task<IActionResult> Create(CreateSupportQuestionViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var supportQuestion = await _supportQuestionService.CreateAsync(
                model.Name,
                model.Email,
                model.Body);

            TempData["SuccessMessage"] = "Your support question has been submitted successfully. We'll get back to you soon!";

            return RedirectToAction("ThankYou", new { id = supportQuestion.Id });
        }
        catch (ValidationException ex)
        {
            ModelState.AddModelError(string.Empty, ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating support question");
            ModelState.AddModelError(string.Empty, "An error occurred while submitting your question. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Thank you page after submitting support question - Web View
    /// </summary>
    [AllowAnonymous]
    [Route("thankyou/{id}")]
    public async Task<IActionResult> ThankYou(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                return RedirectToAction("Index", "Home");
            }

            return View(supportQuestion);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading thank you page for support question ID: {Id}", id);
            return RedirectToAction("Index", "Home");
        }
    }

    /// <summary>
    /// Update support question status (Admin/Manager only) - Web View
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "WebUser")]
    [ValidateAntiForgeryToken]
    [Route("update-status")]
    public async Task<IActionResult> UpdateStatus(Guid id, string status)
    {
        try
        {
            var updatedBy = User.Identity?.Name ?? "System";
            await _supportQuestionService.UpdateStatusAsync(id, status, updatedBy);
            TempData["SuccessMessage"] = "Support question status updated successfully.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating support question status for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error updating support question status. Please try again.";
        }

        return RedirectToAction(nameof(Details), new { id });
    }

    /// <summary>
    /// Assign support question to user (Admin/Manager only) - Web View
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "WebUser")]
    [ValidateAntiForgeryToken]
    [Route("assign")]
    public async Task<IActionResult> Assign(Guid id, Guid? assignedToUserId)
    {
        try
        {
            var updatedBy = User.Identity?.Name ?? "System";
            await _supportQuestionService.AssignToUserAsync(id, assignedToUserId, updatedBy);
            TempData["SuccessMessage"] = assignedToUserId.HasValue
                ? "Support question assigned successfully."
                : "Support question unassigned successfully.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning support question for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error assigning support question. Please try again.";
        }

        return RedirectToAction(nameof(Details), new { id });
    }

    #endregion

    #region API Actions

    /// <summary>
    /// Get all support questions with optional filtering and pagination - API
    /// </summary>
    /// <param name="filter">Filter and pagination parameters</param>
    /// <returns>Paginated list of support questions</returns>
    [HttpGet("api")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestions([FromQuery] SupportQuestionFilterDto filter)
    {
        try
        {
            var skip = (filter.Page - 1) * filter.PageSize;
            var supportQuestions = await _supportQuestionService.GetAllAsync(
                filter.Status,
                filter.AssignedToUserId,
                filter.Email,
                skip,
                filter.PageSize);

            var totalCount = await _supportQuestionService.GetCountAsync(
                filter.Status,
                filter.AssignedToUserId,
                filter.Email);

            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            var result = new PagedSupportQuestionDto
            {
                Items = supportQuestionDtos,
                TotalCount = totalCount,
                Page = filter.Page,
                PageSize = filter.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize),
                HasNextPage = filter.Page * filter.PageSize < totalCount,
                HasPreviousPage = filter.Page > 1
            };

            return CreateResponse(result, $"Retrieved {supportQuestionDtos.Count} support questions");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support question by ID - API
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <returns>Support question details</returns>
    [HttpGet("api/{id:guid}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestion(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                return CreateErrorResponse("Support question not found", 404);
            }

            return CreateResponse(supportQuestion.ToDto(), "Support question retrieved successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Create a new support question (public endpoint) - API
    /// </summary>
    /// <param name="createDto">Support question creation data</param>
    /// <returns>Created support question</returns>
    [HttpPost("api")]
    [AllowAnonymous]
    public async Task<IActionResult> CreateSupportQuestion([FromBody] CreateSupportQuestionDto createDto)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.CreateAsync(
                createDto.Name,
                createDto.Email,
                createDto.Body);

            return CreatedAtAction(
                nameof(GetSupportQuestion),
                new { id = supportQuestion.Id },
                supportQuestion.ToDto());
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error creating support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Update support question status - API
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="updateDto">Status update data</param>
    /// <returns>Updated support question</returns>
    [HttpPut("api/{id:guid}/status")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> UpdateSupportQuestionStatus(Guid id, [FromBody] UpdateSupportQuestionStatusDto updateDto)
    {
        try
        {
            var updatedBy = CurrentUserEmail ?? "System";
            var supportQuestion = await _supportQuestionService.UpdateStatusAsync(id, updateDto.ProcessingStatus, updatedBy);

            return CreateResponse(supportQuestion.ToDto(), "Support question status updated successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error updating support question status: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Assign support question to a user
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="assignDto">Assignment data</param>
    /// <returns>Updated support question</returns>
    [HttpPut("api/{id:guid}/assign")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireManagerRole)]
    public async Task<IActionResult> AssignSupportQuestion(Guid id, [FromBody] AssignSupportQuestionDto assignDto)
    {
        try
        {
            var updatedBy = CurrentUserEmail ?? "System";
            var supportQuestion = await _supportQuestionService.AssignToUserAsync(id, assignDto.AssignedToUserId, updatedBy);

            return CreateResponse(supportQuestion.ToDto(), "Support question assigned successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error assigning support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Delete support question
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("api/{id:guid}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeleteSupportQuestion(Guid id)
    {
        try
        {
            var deletedBy = CurrentUserEmail ?? "System";
            await _supportQuestionService.DeleteAsync(id, deletedBy);

            return CreateResponse<object>(null, "Support question deleted successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error deleting support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions by status
    /// </summary>
    /// <param name="status">Processing status</param>
    /// <returns>List of support questions with the specified status</returns>
    [HttpGet("api/status/{status}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestionsByStatus(string status)
    {
        try
        {
            var supportQuestions = await _supportQuestionService.GetByStatusAsync(status);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} support questions with status '{status}'");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions by status: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions assigned to current user
    /// </summary>
    /// <returns>List of support questions assigned to the current user</returns>
    [HttpGet("api/my-assignments")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetMySupportQuestions()
    {
        try
        {
            if (!Guid.TryParse(CurrentUserId, out var userId))
            {
                return CreateErrorResponse("Invalid user ID", 400);
            }

            var supportQuestions = await _supportQuestionService.GetByAssignedUserAsync(userId);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} assigned support questions");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving assigned support questions: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions by email address
    /// </summary>
    /// <param name="email">Email address</param>
    /// <returns>List of support questions from the specified email</returns>
    [HttpGet("api/email/{email}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestionsByEmail(string email)
    {
        try
        {
            var supportQuestions = await _supportQuestionService.GetByEmailAsync(email);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} support questions from email '{email}'");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions by email: {ex.Message}", 500);
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Gets the current user ID from claims
    /// </summary>
    protected string? CurrentUserId => User?.FindFirst("user_id")?.Value;

    /// <summary>
    /// Gets the current user email from claims
    /// </summary>
    protected string? CurrentUserEmail => User?.FindFirst("email")?.Value;

    /// <summary>
    /// Creates a standardized API response
    /// </summary>
    protected IActionResult CreateResponse<T>(T data, string? message = null)
    {
        return Ok(new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        });
    }

    /// <summary>
    /// Creates a standardized error response
    /// </summary>
    protected IActionResult CreateErrorResponse(string message, int statusCode = 400)
    {
        return StatusCode(statusCode, new ApiResponse<object>
        {
            Success = false,
            Message = message
        });
    }

    #endregion
}
