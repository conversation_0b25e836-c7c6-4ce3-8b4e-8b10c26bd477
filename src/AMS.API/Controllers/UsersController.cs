using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Core.Exceptions;
using AMS.API.DTOs;
using AMS.API.Models;
using AMS.API.Extensions;
using AMS.Core.Entities;

namespace AMS.API.Controllers;

/// <summary>
/// Unified controller for user management - handles both web views and API endpoints
/// </summary>
/// <remarks>
/// This controller provides comprehensive user management functionality including:
/// - Web UI views for user management (Admin only)
/// - API endpoints for programmatic access
/// - User CRUD operations
/// - User search and filtering
/// - Password management and account activation/deactivation
///
/// Web routes: /users/* (returns views)
/// API routes: /users/api/* (returns JSON)
/// Most endpoints require authentication and appropriate role-based permissions.
/// </remarks>
[Route("users")]
public class UsersController : Controller
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(
        IUserService userService,
        ILogger<UsersController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    #region Web View Actions

    /// <summary>
    /// List all users - Web View (Admin only)
    /// </summary>
    [Authorize(Policy = "WebAdministrator")]
    [Route("")]
    [Route("index")]
    public async Task<IActionResult> Index(string? search = null, string? role = null, bool? isActive = null)
    {
        try
        {
            IEnumerable<User> users;

            if (!string.IsNullOrEmpty(search))
            {
                users = await _userService.SearchUsersAsync(search);
            }
            else if (!string.IsNullOrEmpty(role))
            {
                users = await _userService.GetUsersByRoleAsync(role);
            }
            else
            {
                users = await _userService.GetAllUsersAsync();
            }

            // Apply active filter if specified
            if (isActive.HasValue)
            {
                users = users.Where(u => u.IsActive == isActive.Value);
            }

            var model = new UserListViewModel
            {
                Users = users.ToList(),
                SearchTerm = search,
                RoleFilter = role,
                IsActiveFilter = isActive
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading users list");
            TempData["ErrorMessage"] = "Error loading users. Please try again.";
            return View(new UserListViewModel());
        }
    }

    /// <summary>
    /// User details - Web View (Admin only)
    /// </summary>
    [Authorize(Policy = "WebAdministrator")]
    [Route("details/{id}")]
    public async Task<IActionResult> Details(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            return View(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user details for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user details. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Create user form - Web View (Admin only)
    /// </summary>
    [Authorize(Policy = "WebAdministrator")]
    [Route("create")]
    public IActionResult Create()
    {
        return View(new CreateUserViewModel());
    }

    /// <summary>
    /// Process create user form - Web View (Admin only)
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "WebAdministrator")]
    [ValidateAntiForgeryToken]
    [Route("create")]
    public async Task<IActionResult> Create(CreateUserViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var user = new User
            {
                FirstName = model.FirstName,
                LastName = model.LastName,
                Email = model.Email,
                Role = model.Role,
                IsActive = model.IsActive,
                PasswordHash = model.Password // Will be hashed by the service
            };

            await _userService.CreateUserAsync(user);

            TempData["SuccessMessage"] = $"User {user.FullName} created successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (DuplicateEmailException ex)
        {
            ModelState.AddModelError("Email", ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            ModelState.AddModelError(string.Empty, "An error occurred while creating the user. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Edit user form - Web View (Admin only)
    /// </summary>
    [Authorize(Policy = "WebAdministrator")]
    [Route("edit/{id}")]
    public async Task<IActionResult> Edit(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            var model = new EditUserViewModel
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Role = user.Role,
                IsActive = user.IsActive
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user for edit, ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user for editing. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Process edit user form - Web View (Admin only)
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "WebAdministrator")]
    [ValidateAntiForgeryToken]
    [Route("edit/{id}")]
    public async Task<IActionResult> Edit(EditUserViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var user = await _userService.GetUserByIdAsync(model.Id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.Email = model.Email;
            user.Role = model.Role;
            user.IsActive = model.IsActive;

            await _userService.UpdateUserAsync(user);

            TempData["SuccessMessage"] = $"User {user.FullName} updated successfully.";
            return RedirectToAction(nameof(Details), new { id = user.Id });
        }
        catch (DuplicateEmailException ex)
        {
            ModelState.AddModelError("Email", ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user");
            ModelState.AddModelError(string.Empty, "An error occurred while updating the user. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Delete user form - Web View (Admin only)
    /// </summary>
    [Authorize(Policy = "WebAdministrator")]
    [Route("delete/{id}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            // Prevent deleting the current user
            if (User.FindFirst("user_id")?.Value == id.ToString())
            {
                TempData["ErrorMessage"] = "You cannot delete your own account.";
                return RedirectToAction(nameof(Index));
            }

            return View(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user for delete, ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user for deletion. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Process delete user - Web View (Admin only)
    /// </summary>
    [HttpPost, ActionName("Delete")]
    [Authorize(Policy = "WebAdministrator")]
    [ValidateAntiForgeryToken]
    [Route("delete/{id}")]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            // Prevent deleting the current user
            if (User.FindFirst("user_id")?.Value == id.ToString())
            {
                TempData["ErrorMessage"] = "You cannot delete your own account.";
                return RedirectToAction(nameof(Index));
            }

            await _userService.DeleteUserAsync(id);

            TempData["SuccessMessage"] = $"User {user.FullName} deleted successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error deleting user. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    #endregion

    #region API Actions

    /// <summary>
    /// Get all users - API
    /// </summary>
    /// <param name="role">Filter by role (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="search">Search term (optional)</param>
    /// <returns>List of users</returns>
    [HttpGet("api")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetUsers(
        [FromQuery] string? role = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? search = null)
    {
        IEnumerable<Core.Entities.User> users;

        if (!string.IsNullOrEmpty(search))
        {
            users = await _userService.SearchUsersAsync(search);
        }
        else if (!string.IsNullOrEmpty(role))
        {
            users = await _userService.GetUsersByRoleAsync(role);
        }
        else if (isActive.HasValue && isActive.Value)
        {
            users = await _userService.GetActiveUsersAsync();
        }
        else
        {
            users = await _userService.GetAllUsersAsync();
        }

        // Apply additional filters if needed
        if (isActive.HasValue && !string.IsNullOrEmpty(search))
        {
            users = users.Where(u => u.IsActive == isActive.Value);
        }

        return CreateResponse(users.ToDto(), "Users retrieved successfully");
    }

    /// <summary>
    /// Get user by ID - API
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User details</returns>
    [HttpGet("api/{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetUser(Guid id)
    {
        var user = await _userService.GetUserByIdAsync(id);
        if (user == null)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse(user.ToDto(), "User retrieved successfully");
    }

    /// <summary>
    /// Create a new user - API
    /// </summary>
    /// <param name="createUserDto">User creation data</param>
    /// <returns>Created user</returns>
    [HttpPost("api")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserDto createUserDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var user = createUserDto.ToEntity();
            user.CreatedBy = CurrentUserEmail;

            var createdUser = await _userService.CreateUserAsync(user);
            return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id }, createdUser.ToDto());
        }
        catch (DuplicateEmailException ex)
        {
            return CreateErrorResponse(ex.Message, 409);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to create user", 500);
        }
    }

    /// <summary>
    /// Update user information - API
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="updateUserDto">User update data</param>
    /// <returns>Updated user</returns>
    [HttpPut("api/{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserDto updateUserDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var user = updateUserDto.ToEntity(id);
            user.UpdatedBy = CurrentUserEmail;

            var updatedUser = await _userService.UpdateUserAsync(user);
            return CreateResponse(updatedUser.ToDto(), "User updated successfully");
        }
        catch (EntityNotFoundException)
        {
            return CreateErrorResponse("User not found", 404);
        }
        catch (DuplicateEmailException ex)
        {
            return CreateErrorResponse(ex.Message, 409);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to update user", 500);
        }
    }

    /// <summary>
    /// Delete user (soft delete) - API
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("api/{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        try
        {
            await _userService.DeleteUserAsync(id);
            return CreateResponse<object>(null, "User deleted successfully");
        }
        catch (EntityNotFoundException)
        {
            return CreateErrorResponse("User not found", 404);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to delete user", 500);
        }
    }

    /// <summary>
    /// Deactivate user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpPost("api/{id}/deactivate")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeactivateUser(Guid id)
    {
        var result = await _userService.DeactivateUserAsync(id);
        if (!result)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse<object>(null, "User deactivated successfully");
    }

    /// <summary>
    /// Activate user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpPost("api/{id}/activate")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> ActivateUser(Guid id)
    {
        var result = await _userService.ActivateUserAsync(id);
        if (!result)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse<object>(null, "User activated successfully");
    }

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="changePasswordDto">Password change data</param>
    /// <returns>Success response</returns>
    [HttpPost("api/change-password")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        if (CurrentUserId == null || !Guid.TryParse(CurrentUserId, out var userId))
        {
            return CreateErrorResponse("User not found", 404);
        }

        try
        {
            var result = await _userService.ChangePasswordAsync(userId, changePasswordDto.CurrentPassword, changePasswordDto.NewPassword);
            if (!result)
            {
                return CreateErrorResponse("Current password is incorrect", 400);
            }

            return CreateResponse<object>(null, "Password changed successfully");
        }
        catch (InvalidPasswordException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to change password", 500);
        }
    }

    /// <summary>
    /// Reset user password (admin only)
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="resetPasswordDto">Password reset data</param>
    /// <returns>Success response</returns>
    [HttpPost("api/{id}/reset-password")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> ResetPassword(Guid id, [FromBody] ResetPasswordDto resetPasswordDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var result = await _userService.ResetPasswordAsync(id, resetPasswordDto.NewPassword);
            if (!result)
            {
                return CreateErrorResponse("User not found", 404);
            }

            return CreateResponse<object>(null, "Password reset successfully");
        }
        catch (InvalidPasswordException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to reset password", 500);
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Gets the current user ID from claims
    /// </summary>
    protected string? CurrentUserId => User?.FindFirst("user_id")?.Value;

    /// <summary>
    /// Gets the current user email from claims
    /// </summary>
    protected string? CurrentUserEmail => User?.FindFirst("email")?.Value;

    /// <summary>
    /// Creates a standardized API response
    /// </summary>
    protected IActionResult CreateResponse<T>(T data, string? message = null)
    {
        return Ok(new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        });
    }

    /// <summary>
    /// Creates a standardized error response
    /// </summary>
    protected IActionResult CreateErrorResponse(string message, int statusCode = 400)
    {
        return StatusCode(statusCode, new ApiResponse<object>
        {
            Success = false,
            Message = message
        });
    }

    #endregion
}
