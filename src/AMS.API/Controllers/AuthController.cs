using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AMS.Core.Interfaces;
using AMS.API.DTOs;
using AMS.API.Extensions;

namespace AMS.API.Controllers;

/// <summary>
/// Controller for authentication operations
/// </summary>
/// <remarks>
/// This controller handles user authentication, token management, and user session operations.
/// All endpoints in this controller are accessible without authentication except where noted.
/// </remarks>
[AllowAnonymous]
[ApiController]
[Route("api/[controller]")]
[Tags("Authentication")]
public class AuthController : BaseController
{
    private readonly IAuthenticationService _authenticationService;
    private readonly IUserService _userService;

    public AuthController(
        IAuthenticationService authenticationService,
        IUserService userService)
    {
        _authenticationService = authenticationService;
        _userService = userService;
    }

    /// <summary>
    /// Authenticate user and return JWT token
    /// </summary>
    /// <param name="request">Login credentials containing email and password</param>
    /// <returns>Authentication result with access token, refresh token, and user information</returns>
    /// <response code="200">Login successful - returns tokens and user data</response>
    /// <response code="400">Invalid request - validation errors</response>
    /// <response code="401">Authentication failed - invalid credentials</response>
    /// <response code="423">Account locked - too many failed attempts</response>
    [HttpPost("login")]
    [ProducesResponseType(typeof(ApiResponse<LoginResponseDto>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    [ProducesResponseType(typeof(ApiResponse<object>), 423)]
    public async Task<IActionResult> Login([FromBody] LoginRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authenticationService.AuthenticateAsync(request.Email, request.Password);

        if (!result.IsSuccess)
        {
            return CreateErrorResponse(result.ErrorMessage ?? "Authentication failed", 401);
        }

        var response = new LoginResponseDto
        {
            AccessToken = result.AccessToken!,
            RefreshToken = result.RefreshToken!,
            ExpiresAt = result.ExpiresAt!.Value,
            User = result.User!.ToDto()
        };

        return CreateResponse(response, "Login successful");
    }

    /// <summary>
    /// Refresh access token using refresh token
    /// </summary>
    /// <param name="refreshToken">Refresh token</param>
    /// <returns>New access token</returns>
    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshToken([FromBody] string refreshToken)
    {
        if (string.IsNullOrEmpty(refreshToken))
        {
            return CreateErrorResponse("Refresh token is required", 400);
        }

        var result = await _authenticationService.RefreshTokenAsync(refreshToken);

        if (!result.IsSuccess)
        {
            return CreateErrorResponse(result.ErrorMessage ?? "Token refresh failed", 401);
        }

        var response = new LoginResponseDto
        {
            AccessToken = result.AccessToken!,
            RefreshToken = result.RefreshToken!,
            ExpiresAt = result.ExpiresAt!.Value,
            User = result.User!.ToDto()
        };

        return CreateResponse(response, "Token refreshed successfully");
    }

    /// <summary>
    /// Logout user and revoke token
    /// </summary>
    /// <returns>Success response</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        
        if (!string.IsNullOrEmpty(token))
        {
            await _authenticationService.RevokeTokenAsync(token);
        }

        return CreateResponse<object>(null, "Logout successful");
    }

    /// <summary>
    /// Get current user information
    /// </summary>
    /// <returns>Current user details</returns>
    [HttpGet("me")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        if (CurrentUserId == null || !Guid.TryParse(CurrentUserId, out var userId))
        {
            return CreateErrorResponse("User not found", 404);
        }

        var user = await _userService.GetUserByIdAsync(userId);
        if (user == null)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse(user.ToDto(), "User retrieved successfully");
    }
}
