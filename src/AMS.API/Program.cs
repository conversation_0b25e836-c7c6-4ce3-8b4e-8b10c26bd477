using AMS.API.Extensions;
using AMS.API.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Validate configuration
builder.Configuration.ValidateConfiguration();

// Add services to the container
builder.Services.AddControllers();

// Add MVC support for web views
builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

// Add session support
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(60);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Note: JWT authentication will be configured in AddAuthenticationServices
// We'll add cookie authentication there to avoid conflicts

// Add API Explorer and Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerDocumentation();

// Add application services
builder.Services.AddDatabaseServices(builder.Configuration);
builder.Services.AddRepositoryServices();
builder.Services.AddApplicationServices();
builder.Services.AddValidationServices();
builder.Services.AddAuthenticationServices(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "AMS API V1");
        c.RoutePrefix = "swagger"; // Move Swagger to /swagger path
    });
}
else
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

// Add validation middleware
app.UseMiddleware<ValidationMiddleware>();

// Add global exception handling middleware
app.UseMiddleware<GlobalExceptionMiddleware>();

// Add authorization middleware
app.UseMiddleware<AuthorizationMiddleware>();

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseSession();

app.UseAuthentication();
app.UseAuthorization();

// Map MVC routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Map API routes
app.MapControllers();

// Initialize database
if (app.Environment.IsDevelopment())
{
    await app.InitializeDatabaseAsync();
}
else
{
    await app.EnsureDatabaseAsync();
}

app.Run();

// Make Program class accessible for testing
public partial class Program { }
