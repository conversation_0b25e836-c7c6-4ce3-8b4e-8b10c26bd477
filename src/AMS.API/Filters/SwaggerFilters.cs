using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Text.Json;

namespace AMS.API.Filters;

/// <summary>
/// Swagger operation filter for default values
/// </summary>
public class SwaggerDefaultValues : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Add operation ID if not present
        if (string.IsNullOrEmpty(operation.OperationId))
        {
            operation.OperationId = context.MethodInfo.Name;
        }

        // Add default responses if not present
        if (!operation.Responses.ContainsKey("400"))
        {
            operation.Responses.Add("400", new OpenApiResponse
            {
                Description = "Bad Request - Invalid input"
            });
        }

        if (!operation.Responses.ContainsKey("500"))
        {
            operation.Responses.Add("500", new OpenApiResponse
            {
                Description = "Internal Server Error"
            });
        }
    }
}

/// <summary>
/// Swagger document filter for additional documentation
/// </summary>
public class SwaggerDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Add custom tags
        swaggerDoc.Tags = new List<OpenApiTag>
        {
            new OpenApiTag
            {
                Name = "Authentication",
                Description = "User authentication and authorization endpoints"
            },
            new OpenApiTag
            {
                Name = "Users",
                Description = "User management operations"
            },
            new OpenApiTag
            {
                Name = "Health",
                Description = "Application health check endpoints"
            }
        };

        // Add servers
        swaggerDoc.Servers = new List<OpenApiServer>
        {
            new OpenApiServer
            {
                Url = "https://localhost:7001",
                Description = "Development server (HTTPS)"
            },
            new OpenApiServer
            {
                Url = "http://localhost:5001",
                Description = "Development server (HTTP)"
            }
        };

        // Remove unwanted paths
        var pathsToRemove = swaggerDoc.Paths
            .Where(x => x.Key.Contains("weatherforecast", StringComparison.OrdinalIgnoreCase))
            .ToList();

        foreach (var path in pathsToRemove)
        {
            swaggerDoc.Paths.Remove(path.Key);
        }
    }
}

/// <summary>
/// Swagger schema filter for custom schema documentation
/// </summary>
public class SwaggerSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type == typeof(DateTime) || context.Type == typeof(DateTime?))
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("\"2023-12-01T10:30:00Z\"");
            schema.Format = "date-time";
        }

        if (context.Type == typeof(Guid) || context.Type == typeof(Guid?))
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("\"123e4567-e89b-12d3-a456-************\"");
            schema.Format = "uuid";
        }

        // Add examples for common types
        if (context.Type.Name == "LoginRequestDto")
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("""
                {
                    "email": "<EMAIL>",
                    "password": "Admin123!"
                }
                """);
        }

        if (context.Type.Name == "CreateUserDto")
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("""
                {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "password": "SecurePass123!",
                    "role": "User"
                }
                """);
        }

        if (context.Type.Name == "UpdateUserDto")
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("""
                {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "role": "User",
                    "isActive": true
                }
                """);
        }

        if (context.Type.Name == "ChangePasswordDto")
        {
            schema.Example = OpenApiAnyFactory.CreateFromJson("""
                {
                    "currentPassword": "OldPass123!",
                    "newPassword": "NewPass123!",
                    "confirmPassword": "NewPass123!"
                }
                """);
        }
    }
}
