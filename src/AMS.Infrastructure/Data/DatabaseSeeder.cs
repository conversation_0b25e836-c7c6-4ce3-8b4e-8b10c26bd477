using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AMS.Core.Entities;
using AMS.Core.Constants;
using AMS.Core.Interfaces;

namespace AMS.Infrastructure.Data;

/// <summary>
/// Service for seeding initial database data
/// </summary>
public class DatabaseSeeder
{
    private readonly ApplicationDbContext _context;
    private readonly IPasswordService _passwordService;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(
        ApplicationDbContext context,
        IPasswordService passwordService,
        ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _passwordService = passwordService;
        _logger = logger;
    }

    /// <summary>
    /// Seeds the database with initial data
    /// </summary>
    public async Task SeedAsync()
    {
        try
        {
            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Apply pending migrations
            if ((await _context.Database.GetPendingMigrationsAsync()).Any())
            {
                _logger.LogInformation("Applying pending migrations...");
                await _context.Database.MigrateAsync();
            }

            // Seed users if none exist
            if (!await _context.Users.AnyAsync())
            {
                _logger.LogInformation("Seeding initial users...");
                await SeedUsersAsync();
            }

            _logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while seeding the database");
            throw;
        }
    }

    /// <summary>
    /// Seeds initial users
    /// </summary>
    private async Task SeedUsersAsync()
    {
        var users = new List<User>
        {
            new User
            {
                Id = Guid.NewGuid(),
                FirstName = "System",
                LastName = "Administrator",
                Email = "<EMAIL>",
                PasswordHash = _passwordService.HashPassword("Admin123!"),
                Role = ApplicationConstants.Roles.Administrator,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new User
            {
                Id = Guid.NewGuid(),
                FirstName = "John",
                LastName = "Manager",
                Email = "<EMAIL>",
                PasswordHash = _passwordService.HashPassword("Manager123!"),
                Role = ApplicationConstants.Roles.Manager,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new User
            {
                Id = Guid.NewGuid(),
                FirstName = "Jane",
                LastName = "User",
                Email = "<EMAIL>",
                PasswordHash = _passwordService.HashPassword("User123!"),
                Role = ApplicationConstants.Roles.User,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Users.AddRangeAsync(users);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Seeded {Count} initial users", users.Count);
    }

    /// <summary>
    /// Resets the database (for development/testing only)
    /// </summary>
    public async Task ResetDatabaseAsync()
    {
        _logger.LogWarning("Resetting database - this will delete all data!");
        
        await _context.Database.EnsureDeletedAsync();
        await _context.Database.EnsureCreatedAsync();
        
        await SeedAsync();
        
        _logger.LogInformation("Database reset completed");
    }
}
