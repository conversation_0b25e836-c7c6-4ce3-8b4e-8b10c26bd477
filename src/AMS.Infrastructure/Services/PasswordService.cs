using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using AMS.Core.Interfaces;
using AMS.Core.Constants;

namespace AMS.Infrastructure.Services;

/// <summary>
/// Service implementation for password operations
/// </summary>
public class PasswordService : IPasswordService
{
    private const int SaltSize = 16;
    private const int HashSize = 32;
    private const int Iterations = 10000;

    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));

        // Generate salt
        using var rng = RandomNumberGenerator.Create();
        var salt = new byte[SaltSize];
        rng.GetBytes(salt);

        // Generate hash
        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
        var hash = pbkdf2.GetBytes(HashSize);

        // Combine salt and hash
        var hashBytes = new byte[SaltSize + HashSize];
        Array.Copy(salt, 0, hashBytes, 0, SaltSize);
        Array.Copy(hash, 0, hashBytes, SaltSize, HashSize);

        return Convert.ToBase64String(hashBytes);
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hashedPassword))
            throw new ArgumentException("Password and hash cannot be null or empty");

        try
        {
            // Extract salt and hash from stored password
            var hashBytes = Convert.FromBase64String(hashedPassword);
            if (hashBytes.Length != SaltSize + HashSize)
                return false;

            var salt = new byte[SaltSize];
            Array.Copy(hashBytes, 0, salt, 0, SaltSize);

            var storedHash = new byte[HashSize];
            Array.Copy(hashBytes, SaltSize, storedHash, 0, HashSize);

            // Generate hash from provided password
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
            var computedHash = pbkdf2.GetBytes(HashSize);

            // Compare hashes
            return CryptographicOperations.FixedTimeEquals(storedHash, computedHash);
        }
        catch
        {
            return false;
        }
    }

    public string GenerateRandomPassword(int length = 12)
    {
        if (length < ApplicationConstants.Authentication.MinPasswordLength)
            length = ApplicationConstants.Authentication.MinPasswordLength;

        const string validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*";
        var password = new StringBuilder();
        
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);

        foreach (var b in bytes)
        {
            password.Append(validChars[b % validChars.Length]);
        }

        // Ensure password meets strength requirements
        var generatedPassword = password.ToString();
        if (!IsPasswordStrong(generatedPassword))
        {
            // If generated password doesn't meet requirements, try again
            return GenerateRandomPassword(length);
        }

        return generatedPassword;
    }

    public bool IsPasswordStrong(string password)
    {
        if (string.IsNullOrEmpty(password))
            return false;

        // Check minimum length
        if (password.Length < ApplicationConstants.Authentication.MinPasswordLength)
            return false;

        // Check for at least one uppercase letter
        if (!Regex.IsMatch(password, @"[A-Z]"))
            return false;

        // Check for at least one lowercase letter
        if (!Regex.IsMatch(password, @"[a-z]"))
            return false;

        // Check for at least one digit
        if (!Regex.IsMatch(password, @"\d"))
            return false;

        // Check for at least one special character
        if (!Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
            return false;

        return true;
    }

    public string GeneratePasswordResetToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[32];
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes);
    }

    public bool ValidatePasswordResetToken(string token, DateTime createdAt, int validityHours = 24)
    {
        if (string.IsNullOrEmpty(token))
            return false;

        // Check if token has expired
        var expiryTime = createdAt.AddHours(validityHours);
        return DateTime.UtcNow <= expiryTime;
    }
}
