using AMS.Core.Constants;
using AMS.Core.Entities;
using AMS.Core.Exceptions;
using AMS.Core.Interfaces;

namespace AMS.Infrastructure.Services;

/// <summary>
/// Service implementation for SupportQuestion business logic
/// </summary>
public class SupportQuestionService : ISupportQuestionService
{
    private readonly ISupportQuestionRepository _supportQuestionRepository;
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;

    public SupportQuestionService(
        ISupportQuestionRepository supportQuestionRepository,
        IUserRepository userRepository,
        IUnitOfWork unitOfWork)
    {
        _supportQuestionRepository = supportQuestionRepository;
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<IEnumerable<SupportQuestion>> GetAllAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetPagedAsync(status, assignedToUserId, email, skip, take, cancellationToken);
    }

    public async Task<int> GetCountAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetCountAsync(status, assignedToUserId, email, cancellationToken);
    }

    public async Task<SupportQuestion?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetByIdAsync(id, cancellationToken);
    }

    public async Task<SupportQuestion> CreateAsync(string name, string email, string body, CancellationToken cancellationToken = default)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(name))
            throw new ValidationException("Name is required");

        if (string.IsNullOrWhiteSpace(email))
            throw new ValidationException("Email is required");

        if (string.IsNullOrWhiteSpace(body))
            throw new ValidationException("Body is required");

        // Create support question
        var supportQuestion = new SupportQuestion
        {
            Name = name.Trim(),
            Email = email.Trim().ToLowerInvariant(),
            Body = body.Trim(),
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        var createdSupportQuestion = await _supportQuestionRepository.AddAsync(supportQuestion, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return createdSupportQuestion;
    }

    public async Task<SupportQuestion> UpdateStatusAsync(Guid id, string status, string updatedBy, CancellationToken cancellationToken = default)
    {
        var supportQuestion = await _supportQuestionRepository.GetByIdAsync(id, cancellationToken);
        if (supportQuestion == null)
            throw new NotFoundException($"Support question with ID {id} not found");

        // Validate status
        if (!IsValidStatus(status))
            throw new ValidationException($"Invalid status: {status}");

        supportQuestion.ProcessingStatus = status;
        supportQuestion.UpdatedAt = DateTime.UtcNow;
        supportQuestion.UpdatedBy = updatedBy;

        var updatedSupportQuestion = await _supportQuestionRepository.UpdateAsync(supportQuestion, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return updatedSupportQuestion;
    }

    public async Task<SupportQuestion> AssignToUserAsync(Guid id, Guid? assignedToUserId, string updatedBy, CancellationToken cancellationToken = default)
    {
        var supportQuestion = await _supportQuestionRepository.GetByIdAsync(id, cancellationToken);
        if (supportQuestion == null)
            throw new NotFoundException($"Support question with ID {id} not found");

        // Validate user exists if assigning to someone
        if (assignedToUserId.HasValue)
        {
            var user = await _userRepository.GetByIdAsync(assignedToUserId.Value, cancellationToken);
            if (user == null)
                throw new NotFoundException($"User with ID {assignedToUserId} not found");
        }

        supportQuestion.AssignedToUserId = assignedToUserId;
        supportQuestion.UpdatedAt = DateTime.UtcNow;
        supportQuestion.UpdatedBy = updatedBy;

        // Auto-update status when assigning
        if (assignedToUserId.HasValue && supportQuestion.ProcessingStatus == ApplicationConstants.SupportQuestionStatus.Pending)
        {
            supportQuestion.ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress;
        }

        var updatedSupportQuestion = await _supportQuestionRepository.UpdateAsync(supportQuestion, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return updatedSupportQuestion;
    }

    public async Task DeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        var supportQuestion = await _supportQuestionRepository.GetByIdAsync(id, cancellationToken);
        if (supportQuestion == null)
            throw new NotFoundException($"Support question with ID {id} not found");

        await _supportQuestionRepository.DeleteAsync(id, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetByStatusAsync(status, cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetByAssignedUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetByAssignedUserAsync(userId, cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _supportQuestionRepository.GetByEmailAsync(email, cancellationToken);
    }

    private static bool IsValidStatus(string status)
    {
        return status == ApplicationConstants.SupportQuestionStatus.Pending ||
               status == ApplicationConstants.SupportQuestionStatus.InProgress ||
               status == ApplicationConstants.SupportQuestionStatus.Resolved ||
               status == ApplicationConstants.SupportQuestionStatus.Closed;
    }
}
