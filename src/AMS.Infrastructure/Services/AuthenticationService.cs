using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using AMS.Core.Entities;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Core.Exceptions;

namespace AMS.Infrastructure.Services;

/// <summary>
/// Service implementation for authentication operations
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;
    private readonly IConfiguration _configuration;

    public AuthenticationService(
        IUserRepository userRepository,
        IPasswordService passwordService,
        IConfiguration configuration)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;
        _configuration = configuration;
    }

    public async Task<AuthenticationResult> AuthenticateAsync(string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByEmailAsync(email, cancellationToken);
            
            if (user == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid email or password"
                };
            }

            if (!user.IsActive)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Account is deactivated"
                };
            }

            if (!_passwordService.VerifyPassword(password, user.PasswordHash))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid email or password"
                };
            }

            // Generate tokens
            var accessToken = await GenerateJwtTokenAsync(user, cancellationToken);
            var refreshToken = await GenerateRefreshTokenAsync(user, cancellationToken);

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user, cancellationToken);

            return new AuthenticationResult
            {
                IsSuccess = true,
                User = user,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresAt = DateTime.UtcNow.AddMinutes(ApplicationConstants.Authentication.TokenExpirationMinutes)
            };
        }
        catch (Exception ex)
        {
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Authentication failed"
            };
        }
    }

    public async Task<string> GenerateJwtTokenAsync(User user, CancellationToken cancellationToken = default)
    {
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey is not configured");
        var issuer = jwtSettings["Issuer"] ?? "AMS";
        var audience = jwtSettings["Audience"] ?? "AMS";

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ApplicationConstants.ClaimTypes.UserId, user.Id.ToString()),
            new Claim(ApplicationConstants.ClaimTypes.Email, user.Email),
            new Claim(ApplicationConstants.ClaimTypes.Role, user.Role),
            new Claim(ApplicationConstants.ClaimTypes.FirstName, user.FirstName),
            new Claim(ApplicationConstants.ClaimTypes.LastName, user.LastName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var token = new JwtSecurityToken(
            issuer: issuer,
            audience: audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(ApplicationConstants.Authentication.TokenExpirationMinutes),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public async Task<bool> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey is not configured");
            var issuer = jwtSettings["Issuer"] ?? "AMS";
            var audience = jwtSettings["Audience"] ?? "AMS";

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var tokenHandler = new JwtSecurityTokenHandler();

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = key,
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            tokenHandler.ValidateToken(token, validationParameters, out _);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<User?> GetUserFromTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(token);

            var userIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == ApplicationConstants.ClaimTypes.UserId);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return null;
            }

            return await _userRepository.GetByIdAsync(userId, cancellationToken);
        }
        catch
        {
            return null;
        }
    }

    public async Task<string> GenerateRefreshTokenAsync(User user, CancellationToken cancellationToken = default)
    {
        // For simplicity, using a GUID as refresh token
        // In production, you might want to store this in database with expiration
        return Guid.NewGuid().ToString();
    }

    public async Task<AuthenticationResult> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        // This is a simplified implementation
        // In production, you would validate the refresh token against stored tokens
        return new AuthenticationResult
        {
            IsSuccess = false,
            ErrorMessage = "Refresh token functionality not implemented"
        };
    }

    public async Task RevokeTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        // This is a simplified implementation
        // In production, you would add the token to a blacklist or revocation list
        await Task.CompletedTask;
    }
}
