using AMS.Core.Entities;
using AMS.Core.Interfaces;
using AMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace AMS.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for SupportQuestion entity
/// </summary>
public class SupportQuestionRepository : Repository<SupportQuestion>, ISupportQuestionRepository
{
    public SupportQuestionRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<SupportQuestion>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(sq => sq.ProcessingStatus == status)
            .Include(sq => sq.AssignedToUser)
            .OrderByDescending(sq => sq.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetByAssignedUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(sq => sq.AssignedToUserId == userId)
            .Include(sq => sq.AssignedToUser)
            .OrderByDescending(sq => sq.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(sq => sq.Email.ToLower() == email.ToLower())
            .Include(sq => sq.AssignedToUser)
            .OrderByDescending(sq => sq.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SupportQuestion>> GetPagedAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrEmpty(status))
        {
            query = query.Where(sq => sq.ProcessingStatus == status);
        }

        if (assignedToUserId.HasValue)
        {
            query = query.Where(sq => sq.AssignedToUserId == assignedToUserId.Value);
        }

        if (!string.IsNullOrEmpty(email))
        {
            query = query.Where(sq => sq.Email.ToLower().Contains(email.ToLower()));
        }

        return await query
            .Include(sq => sq.AssignedToUser)
            .OrderByDescending(sq => sq.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountAsync(
        string? status = null,
        Guid? assignedToUserId = null,
        string? email = null,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrEmpty(status))
        {
            query = query.Where(sq => sq.ProcessingStatus == status);
        }

        if (assignedToUserId.HasValue)
        {
            query = query.Where(sq => sq.AssignedToUserId == assignedToUserId.Value);
        }

        if (!string.IsNullOrEmpty(email))
        {
            query = query.Where(sq => sq.Email.ToLower().Contains(email.ToLower()));
        }

        return await query.CountAsync(cancellationToken);
    }

    public override async Task<SupportQuestion?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(sq => sq.AssignedToUser)
            .FirstOrDefaultAsync(sq => sq.Id == id, cancellationToken);
    }

    public override async Task<IEnumerable<SupportQuestion>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(sq => sq.AssignedToUser)
            .OrderByDescending(sq => sq.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
