using AMS.Core.Constants;
using AMS.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AMS.Infrastructure.Configurations;

/// <summary>
/// Entity Framework configuration for SupportQuestion entity
/// </summary>
public class SupportQuestionConfiguration : IEntityTypeConfiguration<SupportQuestion>
{
    public void Configure(EntityTypeBuilder<SupportQuestion> builder)
    {
        // Table configuration
        builder.ToTable("SupportQuestions");

        // Primary key
        builder.HasKey(sq => sq.Id);

        // Properties configuration
        builder.Property(sq => sq.Name)
            .IsRequired()
            .HasMaxLength(ApplicationConstants.Validation.MaxSupportQuestionNameLength);

        builder.Property(sq => sq.Email)
            .IsRequired()
            .HasMaxLength(ApplicationConstants.Validation.MaxEmailLength);

        builder.Property(sq => sq.Body)
            .IsRequired()
            .HasMaxLength(ApplicationConstants.Validation.MaxSupportQuestionBodyLength);

        builder.Property(sq => sq.ProcessingStatus)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue(ApplicationConstants.SupportQuestionStatus.Pending);

        builder.Property(sq => sq.CreatedAt)
            .IsRequired();

        builder.Property(sq => sq.UpdatedAt);

        builder.Property(sq => sq.CreatedBy)
            .HasMaxLength(100);

        builder.Property(sq => sq.UpdatedBy)
            .HasMaxLength(100);

        builder.Property(sq => sq.IsDeleted)
            .HasDefaultValue(false);

        // Foreign key relationships
        builder.HasOne(sq => sq.AssignedToUser)
            .WithMany()
            .HasForeignKey(sq => sq.AssignedToUserId)
            .OnDelete(DeleteBehavior.SetNull);

        // Indexes
        builder.HasIndex(sq => sq.Email)
            .HasDatabaseName("IX_SupportQuestions_Email");

        builder.HasIndex(sq => sq.ProcessingStatus)
            .HasDatabaseName("IX_SupportQuestions_ProcessingStatus");

        builder.HasIndex(sq => sq.CreatedAt)
            .HasDatabaseName("IX_SupportQuestions_CreatedAt");

        builder.HasIndex(sq => sq.AssignedToUserId)
            .HasDatabaseName("IX_SupportQuestions_AssignedToUserId");
    }
}
