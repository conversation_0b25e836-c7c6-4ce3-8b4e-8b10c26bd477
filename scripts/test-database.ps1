# Database Connection Test Script for AMS Application (PowerShell)

Write-Host "=== AMS Database Connection Test ===" -ForegroundColor Cyan
Write-Host ""

# Default values - can be overridden by environment variables
$DB_HOST = if ($env:DB_HOST) { $env:DB_HOST } else { "localhost" }
$DB_PORT = if ($env:DB_PORT) { $env:DB_PORT } else { "5432" }
$DB_NAME = if ($env:DB_NAME) { $env:DB_NAME } else { "ams_development" }
$DB_USER = if ($env:DB_USER) { $env:DB_USER } else { "ams_user" }
$DB_PASSWORD = if ($env:DB_PASSWORD) { $env:DB_PASSWORD } else { "ams_dev_password" }

# Set the connection string environment variable for .NET
# .NET Configuration automatically reads environment variables with __ notation
$env:ConnectionStrings__DefaultConnection = "Host=$DB_HOST;Port=$DB_PORT;Database=$DB_NAME;Username=$DB_USER;Password=$DB_PASSWORD"

Write-Host "Testing connection to PostgreSQL database..."
Write-Host "Host: $DB_HOST"
Write-Host "Port: $DB_PORT"
Write-Host "Database: $DB_NAME"
Write-Host "User: $DB_USER"
Write-Host ""

# Test 1: Check if PostgreSQL is running
Write-Host "1. Checking if PostgreSQL is running..." -ForegroundColor Yellow
try {
    $pgReady = Get-Command pg_isready -ErrorAction SilentlyContinue
    if ($pgReady) {
        $result = & pg_isready -h $DB_HOST -p $DB_PORT
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ PostgreSQL is running" -ForegroundColor Green
        } else {
            Write-Host "✗ PostgreSQL is not running or not accessible" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠ pg_isready not found, skipping PostgreSQL status check" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Could not check PostgreSQL status" -ForegroundColor Yellow
}

# Test 2: Test database connection
Write-Host ""
Write-Host "2. Testing database connection..." -ForegroundColor Yellow
try {
    $psql = Get-Command psql -ErrorAction SilentlyContinue
    if ($psql) {
        $env:PGPASSWORD = $DB_PASSWORD
        $result = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Database connection successful" -ForegroundColor Green
        } else {
            Write-Host "✗ Database connection failed" -ForegroundColor Red
            Write-Host "Please check:"
            Write-Host "  - Database exists: $DB_NAME"
            Write-Host "  - User exists: $DB_USER"
            Write-Host "  - Password is correct"
            Write-Host "  - User has access to database"
            exit 1
        }
    } else {
        Write-Host "⚠ psql not found, skipping direct database connection test" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Could not test database connection" -ForegroundColor Yellow
}

# Test 3: Test Entity Framework connection
Write-Host ""
Write-Host "3. Testing Entity Framework connection..." -ForegroundColor Yellow
try {
    Set-Location (Split-Path $PSScriptRoot -Parent)
    $result = & dotnet ef dbcontext info --project src/AMS.Infrastructure --startup-project src/AMS.API 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Entity Framework can connect to database" -ForegroundColor Green
    } else {
        Write-Host "✗ Entity Framework connection failed" -ForegroundColor Red
        Write-Host "Please check your connection string in appsettings.json"
        exit 1
    }
} catch {
    Write-Host "✗ Entity Framework connection test failed" -ForegroundColor Red
    exit 1
}

# Test 4: Check migrations status
Write-Host ""
Write-Host "4. Checking migrations status..." -ForegroundColor Yellow
try {
    $migrations = & dotnet ef migrations list --project src/AMS.Infrastructure --startup-project src/AMS.API --no-build 2>$null
    $pendingCount = ($migrations | Where-Object { $_ -match "Pending" }).Count
    
    if ($pendingCount -eq 0) {
        Write-Host "✓ All migrations are applied" -ForegroundColor Green
    } else {
        Write-Host "⚠ There are $pendingCount pending migrations" -ForegroundColor Yellow
        Write-Host "Run: dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API"
    }
} catch {
    Write-Host "⚠ Could not check migrations status" -ForegroundColor Yellow
}

# Test 5: Test application startup
Write-Host ""
Write-Host "5. Testing application configuration..." -ForegroundColor Yellow
try {
    $result = & dotnet build src/AMS.API 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Application builds successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Application build failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Application build test failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Database connection test completed successfully! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Run migrations: dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API"
Write-Host "2. Start the application: dotnet run --project src/AMS.API"
Write-Host "3. Test API endpoints at: https://localhost:7001/swagger"
Write-Host ""
Write-Host "Default test users:"
Write-Host "  Administrator: <EMAIL> / Admin123!"
Write-Host "  Manager: <EMAIL> / Manager123!"
Write-Host "  User: <EMAIL> / User123!"
