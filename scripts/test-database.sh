#!/bin/bash

# Database Connection Test Script for AMS Application

echo "=== AMS Database Connection Test ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values - can be overridden by environment variables
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-ams_development}
DB_USER=${DB_USER:-ams_user}
DB_PASSWORD=${DB_PASSWORD:-ams_dev_password}

# Set the connection string environment variable for .NET
# .NET Configuration automatically reads environment variables with __ notation
export ConnectionStrings__DefaultConnection="Host=$DB_HOST;Port=$DB_PORT;Database=$DB_NAME;Username=$DB_USER;Password=$DB_PASSWORD"

echo "Testing connection to PostgreSQL database..."
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""

# Test 1: Check if PostgreSQL is running
echo "1. Checking if PostgreSQL is running..."
if command -v pg_isready &> /dev/null; then
    if pg_isready -h $DB_HOST -p $DB_PORT; then
        echo -e "${GREEN}✓ PostgreSQL is running${NC}"
    else
        echo -e "${RED}✗ PostgreSQL is not running or not accessible${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠ pg_isready not found, skipping PostgreSQL status check${NC}"
fi

# Test 2: Test database connection
echo ""
echo "2. Testing database connection..."
if command -v psql &> /dev/null; then
    export PGPASSWORD=$DB_PASSWORD
    if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        echo -e "${GREEN}✓ Database connection successful${NC}"
    else
        echo -e "${RED}✗ Database connection failed${NC}"
        echo "Please check:"
        echo "  - Database exists: $DB_NAME"
        echo "  - User exists: $DB_USER"
        echo "  - Password is correct"
        echo "  - User has access to database"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠ psql not found, skipping direct database connection test${NC}"
fi

# Test 3: Test Entity Framework connection
echo ""
echo "3. Testing Entity Framework connection..."
cd "$(dirname "$0")/.."

if dotnet ef dbcontext info --project src/AMS.Infrastructure --startup-project src/AMS.API &> /dev/null; then
    echo -e "${GREEN}✓ Entity Framework can connect to database${NC}"
else
    echo -e "${RED}✗ Entity Framework connection failed${NC}"
    echo "Please check your connection string in appsettings.json"
    exit 1
fi

# Test 4: Check migrations status
echo ""
echo "4. Checking migrations status..."
PENDING_MIGRATIONS=$(dotnet ef migrations list --project src/AMS.Infrastructure --startup-project src/AMS.API --no-build 2>/dev/null | grep -c "Pending")

if [ "$PENDING_MIGRATIONS" -eq 0 ]; then
    echo -e "${GREEN}✓ All migrations are applied${NC}"
else
    echo -e "${YELLOW}⚠ There are $PENDING_MIGRATIONS pending migrations${NC}"
    echo "Run: dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API"
fi

# Test 5: Test application startup (optional)
echo ""
echo "5. Testing application configuration..."
if dotnet build src/AMS.API &> /dev/null; then
    echo -e "${GREEN}✓ Application builds successfully${NC}"
else
    echo -e "${RED}✗ Application build failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== Database connection test completed successfully! ===${NC}"
echo ""
echo "Next steps:"
echo "1. Run migrations: dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API"
echo "2. Start the application: dotnet run --project src/AMS.API"
echo "3. Test API endpoints at: https://localhost:7001/swagger"
echo ""
echo "Default test users:"
echo "  Administrator: <EMAIL> / Admin123!"
echo "  Manager: <EMAIL> / Manager123!"
echo "  User: <EMAIL> / User123!"
