# Security Configuration Guide

## Overview

This document outlines the security configuration and best practices for the AMS application.

## JWT Authentication

### Configuration

The application uses JWT (JSON Web Tokens) for authentication. Configure the following settings:

```json
{
  "JwtSettings": {
    "SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long",
    "Issuer": "AMS",
    "Audience": "AMS"
  }
}
```

### Security Requirements

- **SecretKey**: Must be at least 32 characters long
- **Issuer**: Should match your application domain
- **Audience**: Should match your application domain
- **Token Expiration**: Default is 60 minutes (configurable)

### Environment Variables

For production, use environment variables instead of appsettings.json:

```bash
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ISSUER=YourDomain
JWT_AUDIENCE=YourDomain
```

## Authorization Policies

The application implements role-based authorization with three levels:

### Roles

- **Administrator**: Full system access
- **Manager**: User management and reporting
- **User**: Basic application access

### Policies

- `RequireAdministratorRole`: Administrator only
- `RequireManagerRole`: Manager and Administrator
- `RequireUserRole`: All authenticated users

## Password Security

### Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character

### Password Hashing

- Uses PBKDF2 with SHA256
- 10,000 iterations
- 16-byte salt
- 32-byte hash

## Database Security

### Connection String Security

Never store database credentials in appsettings.json for production:

```bash
# Environment variable
ConnectionStrings__DefaultConnection="Host=localhost;Database=ams_development;Username=ams_user;Password=secure_password"
```

### Database User Permissions

Create a dedicated database user with minimal required permissions:

```sql
-- Create user
CREATE USER ams_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT CONNECT ON DATABASE ams_development TO ams_user;
GRANT USAGE ON SCHEMA public TO ams_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO ams_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO ams_user;
```

## HTTPS Configuration

### Development

The application is configured to use HTTPS in development with self-signed certificates.

### Production

- Use valid SSL certificates
- Configure HSTS (HTTP Strict Transport Security)
- Implement certificate pinning if required

## Security Headers

The application should implement the following security headers:

```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});
```

## Rate Limiting

Consider implementing rate limiting for API endpoints:

```csharp
// Example rate limiting configuration
services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("AuthPolicy", opt =>
    {
        opt.PermitLimit = 5;
        opt.Window = TimeSpan.FromMinutes(1);
    });
});
```

## Audit Logging

The application includes audit fields in the BaseEntity:

- CreatedAt
- UpdatedAt
- CreatedBy
- UpdatedBy
- IsDeleted (soft delete)

## Security Checklist

### Development

- [ ] Use development-specific JWT secrets
- [ ] Enable detailed logging for debugging
- [ ] Use local database with test data

### Production

- [ ] Use environment variables for all secrets
- [ ] Implement proper SSL certificates
- [ ] Configure secure database connections
- [ ] Enable audit logging
- [ ] Implement rate limiting
- [ ] Configure security headers
- [ ] Regular security updates
- [ ] Monitor for suspicious activities

## Incident Response

### Security Breach Response

1. Immediately revoke all JWT tokens
2. Force password reset for all users
3. Review audit logs for suspicious activity
4. Update JWT secret key
5. Notify affected users
6. Document the incident

### Token Compromise

1. Add compromised tokens to blacklist
2. Force user re-authentication
3. Review user activity logs
4. Consider temporary account suspension

## Compliance

### Data Protection

- Implement GDPR compliance if applicable
- Ensure proper data encryption
- Regular data backups
- Secure data disposal

### Access Control

- Regular access reviews
- Principle of least privilege
- Multi-factor authentication (future enhancement)
- Session management
