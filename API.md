# AMS API Documentation

## Overview

The AMS WebAPI is a RESTful web service built with .NET 8 and Clean Architecture principles. It provides comprehensive user management and support question functionality with JWT-based authentication and role-based authorization.

## Base URL

- **Development**: `https://localhost:7001` (HTTPS) or `http://localhost:5001` (HTTP)
- **Production**: `https://your-domain.com/api`

## Authentication

The API uses JWT (JSON Web Token) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

Use the `/api/auth/login` endpoint to obtain an access token:

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/login

Authenticate user and receive JWT tokens.

**Request Body:**

```json
{
  "email": "string",
  "password": "string"
}
```

**Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "550e8400-e29b-41d4-a716-446655440000",
    "expiresAt": "2023-12-01T11:30:00Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "User",
      "isActive": true,
      "createdAt": "2023-01-01T10:00:00Z",
      "fullName": "John Doe"
    }
  },
  "message": "Login successful",
  "timestamp": "2023-12-01T10:30:00Z"
}
```

#### POST /api/auth/refresh

Refresh access token using refresh token.

#### POST /api/auth/logout

Logout user and revoke token.

#### GET /api/auth/me

Get current authenticated user information.

### User Management Endpoints

#### GET /users/api

Get all users with optional filtering.

**Query Parameters:**

- `role` (string, optional): Filter by user role
- `isActive` (boolean, optional): Filter by active status
- `search` (string, optional): Search in name and email

**Authorization:** Requires `User` role or higher

**Response (200 OK):**

```json
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "User",
      "isActive": true,
      "lastLoginAt": "2023-12-01T09:00:00Z",
      "createdAt": "2023-01-01T10:00:00Z",
      "fullName": "John Doe"
    }
  ],
  "message": "Users retrieved successfully",
  "timestamp": "2023-12-01T10:30:00Z"
}
```

#### GET /api/users/{id}

Get user by ID.

**Authorization:** Requires `User` role or higher

#### POST /api/users

Create a new user.

**Authorization:** Requires `Administrator` role

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "User"
}
```

#### PUT /api/users/{id}

Update user information.

**Authorization:** Requires `Administrator` role

#### DELETE /api/users/{id}

Delete user (soft delete).

**Authorization:** Requires `Administrator` role

#### POST /api/users/{id}/activate

Activate user account.

**Authorization:** Requires `Administrator` role

#### POST /api/users/{id}/deactivate

Deactivate user account.

**Authorization:** Requires `Administrator` role

#### POST /users/api/change-password

Change current user's password.

**Authorization:** Requires authentication

**Request Body:**

```json
{
  "currentPassword": "OldPass123!",
  "newPassword": "NewPass123!",
  "confirmPassword": "NewPass123!"
}
```

#### POST /users/api/{id}/reset-password

Reset user password (admin only).

**Authorization:** Requires `Administrator` role

### Support Questions Endpoints

#### GET /support-questions/api

Get all support questions with optional filtering and pagination.

**Authorization:** Requires `User` role or higher

**Query Parameters:**

- `status` (optional): Filter by processing status (Pending, InProgress, Resolved, Closed)
- `assignedToUserId` (optional): Filter by assigned user ID
- `email` (optional): Filter by email address
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 50, max: 100)

**Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "guid",
        "name": "John Doe",
        "email": "<EMAIL>",
        "body": "I need help with my account",
        "processingStatus": "Pending",
        "assignedToUserId": "guid",
        "assignedToUser": {
          "id": "guid",
          "firstName": "Admin",
          "lastName": "User",
          "email": "<EMAIL>"
        },
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": null,
        "isPending": true,
        "isInProgress": false,
        "isResolved": false,
        "isClosed": false
      }
    ],
    "totalCount": 1,
    "page": 1,
    "pageSize": 50,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "message": "Retrieved 1 support questions"
}
```

#### GET /support-questions/api/{id}

Get support question by ID.

**Authorization:** Requires `User` role or higher

**Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "id": "guid",
    "name": "John Doe",
    "email": "<EMAIL>",
    "body": "I need help with my account",
    "processingStatus": "Pending",
    "assignedToUserId": null,
    "assignedToUser": null,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": null,
    "isPending": true,
    "isInProgress": false,
    "isResolved": false,
    "isClosed": false
  },
  "message": "Support question retrieved successfully"
}
```

#### POST /support-questions/api

Create a new support question (public endpoint - no authentication required).

**Request Body:**

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "body": "I need help with my account. I can't log in and reset password doesn't work."
}
```

**Response (201 Created):**

```json
{
  "id": "guid",
  "name": "John Doe",
  "email": "<EMAIL>",
  "body": "I need help with my account. I can't log in and reset password doesn't work.",
  "processingStatus": "Pending",
  "assignedToUserId": null,
  "assignedToUser": null,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": null,
  "isPending": true,
  "isInProgress": false,
  "isResolved": false,
  "isClosed": false
}
```

#### PUT /support-questions/api/{id}/status

Update support question processing status.

**Authorization:** Requires `User` role or higher

**Request Body:**

```json
{
  "processingStatus": "InProgress"
}
```

**Valid Status Values:**

- `Pending` - Initial status when created
- `InProgress` - Being worked on
- `Resolved` - Issue has been resolved
- `Closed` - Question is closed

#### PUT /support-questions/api/{id}/assign

Assign support question to a user.

**Authorization:** Requires `Manager` role or higher

**Request Body:**

```json
{
  "assignedToUserId": "guid"
}
```

**Note:** Set `assignedToUserId` to `null` to unassign the question.

#### DELETE /support-questions/api/{id}

Delete support question (soft delete).

**Authorization:** Requires `Administrator` role

#### GET /support-questions/api/status/{status}

Get support questions by processing status.

**Authorization:** Requires `User` role or higher

**Path Parameters:**

- `status`: Processing status (Pending, InProgress, Resolved, Closed)

#### GET /support-questions/api/my-assignments

Get support questions assigned to the current user.

**Authorization:** Requires `User` role or higher

#### GET /support-questions/api/email/{email}

Get support questions submitted by a specific email address.

**Authorization:** Requires `User` role or higher

**Path Parameters:**

- `email`: Email address to search for

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "data": <response_data>,
  "message": "Operation successful",
  "timestamp": "2023-12-01T10:30:00Z"
}
```

### Error Response

```json
{
  "success": false,
  "statusCode": 400,
  "message": "Error description",
  "traceId": "0HN7GKDVQ1234:00000001",
  "timestamp": "2023-12-01T10:30:00Z"
}
```

### Validation Error Response

```json
{
  "success": false,
  "statusCode": 400,
  "message": "Validation failed",
  "traceId": "0HN7GKDVQ1234:00000001",
  "errors": {
    "email": ["Email is required", "Invalid email format"],
    "password": ["Password is too short"]
  },
  "timestamp": "2023-12-01T10:30:00Z"
}
```

## HTTP Status Codes

| Code | Description                             |
| ---- | --------------------------------------- |
| 200  | OK - Request successful                 |
| 201  | Created - Resource created successfully |
| 400  | Bad Request - Invalid request data      |
| 401  | Unauthorized - Authentication required  |
| 403  | Forbidden - Insufficient permissions    |
| 404  | Not Found - Resource not found          |
| 409  | Conflict - Resource already exists      |
| 423  | Locked - Account locked                 |
| 500  | Internal Server Error - Server error    |

## User Roles

| Role              | Description           | Permissions                  |
| ----------------- | --------------------- | ---------------------------- |
| **Administrator** | Full system access    | All operations               |
| **Manager**       | Management operations | User management, reporting   |
| **User**          | Basic access          | View operations, own profile |

## Rate Limiting

API endpoints may be rate-limited to prevent abuse:

- Authentication endpoints: 5 requests per minute
- General endpoints: 100 requests per minute per user

## Error Handling

The API implements comprehensive error handling:

1. **Validation Errors**: Field-level validation with detailed messages
2. **Business Logic Errors**: Domain-specific error responses
3. **Authentication Errors**: Clear authentication failure messages
4. **Authorization Errors**: Permission-based error responses
5. **System Errors**: Generic error messages for security

## Security

### Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character

### Token Security

- JWT tokens expire after 60 minutes
- Refresh tokens expire after 7 days
- Tokens are revoked on logout
- Secure token storage recommended

## Testing

### Test Users

The system includes pre-seeded test users:

| Email           | Password    | Role          |
| --------------- | ----------- | ------------- |
| <EMAIL>   | Admin123!   | Administrator |
| <EMAIL> | Manager123! | Manager       |
| <EMAIL>    | User123!    | User          |

### Swagger UI

Interactive API documentation is available at:

- Development: `https://localhost:7001/swagger`
- The Swagger UI provides a complete interface for testing all endpoints

## SDK and Client Libraries

### cURL Examples

**Login:**

```bash
curl -X POST "https://localhost:7001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}'
```

**Get Users:**

```bash
curl -X GET "https://localhost:7001/api/users" \
  -H "Authorization: Bearer <your-token>"
```

### JavaScript/TypeScript

```javascript
// Login
const response = await fetch("/api/auth/login", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ email: "<EMAIL>", password: "Admin123!" }),
});

// Get Users
const users = await fetch("/api/users", {
  headers: { Authorization: `Bearer ${token}` },
});
```

## Versioning

The API uses URL versioning:

- Current version: `v1`
- Base path: `/api/v1/` (future versions)

## Support

For API support and questions:

- Documentation: This document and Swagger UI
- Issues: GitHub repository issues
- Email: <EMAIL>
