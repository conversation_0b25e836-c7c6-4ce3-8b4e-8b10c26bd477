using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using AMS.Infrastructure.Data;

namespace AMS.Infrastructure.Tests.Helpers;

/// <summary>
/// Factory for creating test database contexts
/// </summary>
public static class TestDbContextFactory
{
    /// <summary>
    /// Creates an in-memory database context for testing
    /// </summary>
    public static ApplicationDbContext CreateInMemoryContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging()
            .EnableDetailedErrors()
            .Options;

        return new ApplicationDbContext(options);
    }

    /// <summary>
    /// Creates a SQLite in-memory database context for testing
    /// </summary>
    public static ApplicationDbContext CreateSqliteInMemoryContext()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlite("DataSource=:memory:")
            .EnableSensitiveDataLogging()
            .EnableDetailedErrors()
            .Options;

        var context = new ApplicationDbContext(options);
        context.Database.OpenConnection();
        context.Database.EnsureCreated();
        
        return context;
    }

    /// <summary>
    /// Creates a database context with seeded test data
    /// </summary>
    public static ApplicationDbContext CreateContextWithData(string? databaseName = null)
    {
        var context = CreateInMemoryContext(databaseName);
        SeedTestData(context);
        return context;
    }

    /// <summary>
    /// Seeds the context with test data
    /// </summary>
    public static void SeedTestData(ApplicationDbContext context)
    {
        // Clear existing data
        context.Users.RemoveRange(context.Users);
        context.SaveChanges();

        // Add test users
        var testUsers = AMS.Core.Tests.Helpers.TestDataBuilder.CreateUsersWithDifferentRoles();
        context.Users.AddRange(testUsers);
        context.SaveChanges();
    }

    /// <summary>
    /// Creates a service provider with test database context
    /// </summary>
    public static IServiceProvider CreateServiceProvider(string? databaseName = null)
    {
        var services = new ServiceCollection();
        
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
                   .EnableSensitiveDataLogging()
                   .EnableDetailedErrors());

        return services.BuildServiceProvider();
    }

    /// <summary>
    /// Creates a scoped database context from service provider
    /// </summary>
    public static ApplicationDbContext CreateScopedContext(IServiceProvider serviceProvider)
    {
        var scope = serviceProvider.CreateScope();
        return scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }
}

/// <summary>
/// Base class for database tests with common setup
/// </summary>
public abstract class DatabaseTestBase : IDisposable
{
    protected ApplicationDbContext Context { get; private set; }
    protected string DatabaseName { get; private set; }

    protected DatabaseTestBase()
    {
        DatabaseName = Guid.NewGuid().ToString();
        Context = TestDbContextFactory.CreateInMemoryContext(DatabaseName);
    }

    /// <summary>
    /// Seeds the test database with initial data
    /// </summary>
    protected virtual void SeedDatabase()
    {
        TestDbContextFactory.SeedTestData(Context);
    }

    /// <summary>
    /// Clears all data from the test database
    /// </summary>
    protected virtual void ClearDatabase()
    {
        Context.Users.RemoveRange(Context.Users);
        Context.SaveChanges();
    }

    /// <summary>
    /// Recreates the database context
    /// </summary>
    protected virtual void RecreateContext()
    {
        Context.Dispose();
        Context = TestDbContextFactory.CreateInMemoryContext(DatabaseName);
    }

    public virtual void Dispose()
    {
        Context?.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Base class for SQLite database tests
/// </summary>
public abstract class SqliteTestBase : IDisposable
{
    protected ApplicationDbContext Context { get; private set; }

    protected SqliteTestBase()
    {
        Context = TestDbContextFactory.CreateSqliteInMemoryContext();
    }

    /// <summary>
    /// Seeds the test database with initial data
    /// </summary>
    protected virtual void SeedDatabase()
    {
        TestDbContextFactory.SeedTestData(Context);
    }

    /// <summary>
    /// Clears all data from the test database
    /// </summary>
    protected virtual void ClearDatabase()
    {
        Context.Users.RemoveRange(Context.Users);
        Context.SaveChanges();
    }

    public virtual void Dispose()
    {
        Context?.Database.CloseConnection();
        Context?.Dispose();
        GC.SuppressFinalize(this);
    }
}
