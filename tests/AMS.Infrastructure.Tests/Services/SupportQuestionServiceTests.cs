using AMS.Core.Constants;
using AMS.Core.Entities;
using AMS.Core.Exceptions;
using AMS.Core.Interfaces;
using AMS.Infrastructure.Services;
using FluentAssertions;
using Moq;

namespace AMS.Infrastructure.Tests.Services;

public class SupportQuestionServiceTests
{
    private readonly Mock<ISupportQuestionRepository> _mockSupportQuestionRepository;
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly SupportQuestionService _service;

    public SupportQuestionServiceTests()
    {
        _mockSupportQuestionRepository = new Mock<ISupportQuestionRepository>();
        _mockUserRepository = new Mock<IUserRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _service = new SupportQuestionService(_mockSupportQuestionRepository.Object, _mockUserRepository.Object, _mockUnitOfWork.Object);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnPagedResults()
    {
        // Arrange
        var supportQuestions = new List<SupportQuestion>
        {
            new() { Name = "John Doe", Email = "<EMAIL>", Body = "Test question 1" },
            new() { Name = "Jane Smith", Email = "<EMAIL>", Body = "Test question 2" }
        };

        _mockSupportQuestionRepository
            .Setup(x => x.GetPagedAsync(null, null, null, 0, 50, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(supportQuestions);
        _mockSupportQuestionRepository.Verify(x => x.GetPagedAsync(null, null, null, 0, 50, default), Times.Once);
    }

    [Fact]
    public async Task GetCountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        _mockSupportQuestionRepository
            .Setup(x => x.GetCountAsync(null, null, null, default))
            .ReturnsAsync(5);

        // Act
        var result = await _service.GetCountAsync();

        // Assert
        result.Should().Be(5);
        _mockSupportQuestionRepository.Verify(x => x.GetCountAsync(null, null, null, default), Times.Once);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnSupportQuestion_WhenExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var supportQuestion = new SupportQuestion { Id = id, Name = "John Doe" };

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync(supportQuestion);

        // Act
        var result = await _service.GetByIdAsync(id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(id);
        result.Name.Should().Be("John Doe");
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync((SupportQuestion?)null);

        // Act
        var result = await _service.GetByIdAsync(id);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateSupportQuestion_WithValidData()
    {
        // Arrange
        var name = "John Doe";
        var email = "<EMAIL>";
        var body = "I need help with my account";
        var expectedSupportQuestion = new SupportQuestion
        {
            Name = name,
            Email = email.ToLowerInvariant(),
            Body = body,
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending
        };

        _mockSupportQuestionRepository
            .Setup(x => x.AddAsync(It.IsAny<SupportQuestion>(), default))
            .ReturnsAsync((SupportQuestion sq, CancellationToken _) => sq);

        // Act
        var result = await _service.CreateAsync(name, email, body);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(name);
        result.Email.Should().Be(email.ToLowerInvariant());
        result.Body.Should().Be(body);
        result.ProcessingStatus.Should().Be(ApplicationConstants.SupportQuestionStatus.Pending);
        _mockSupportQuestionRepository.Verify(x => x.AddAsync(It.IsAny<SupportQuestion>(), default), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(default), Times.Once);
    }

    [Theory]
    [InlineData("", "<EMAIL>", "Test body")]
    [InlineData("   ", "<EMAIL>", "Test body")]
    [InlineData("John Doe", "", "Test body")]
    [InlineData("John Doe", "   ", "Test body")]
    [InlineData("John Doe", "<EMAIL>", "")]
    [InlineData("John Doe", "<EMAIL>", "   ")]
    public async Task CreateAsync_ShouldThrowValidationException_WithInvalidData(string name, string email, string body)
    {
        // Act & Assert
        await _service.Invoking(s => s.CreateAsync(name, email, body))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task UpdateStatusAsync_ShouldUpdateStatus_WhenSupportQuestionExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var supportQuestion = new SupportQuestion 
        { 
            Id = id, 
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending 
        };
        var newStatus = ApplicationConstants.SupportQuestionStatus.InProgress;
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync(supportQuestion);

        _mockSupportQuestionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SupportQuestion>(), default))
            .ReturnsAsync((SupportQuestion sq, CancellationToken _) => sq);

        // Act
        var result = await _service.UpdateStatusAsync(id, newStatus, updatedBy);

        // Assert
        result.Should().NotBeNull();
        result.ProcessingStatus.Should().Be(newStatus);
        result.UpdatedBy.Should().Be(updatedBy);
        result.UpdatedAt.Should().NotBeNull();
        _mockSupportQuestionRepository.Verify(x => x.UpdateAsync(It.IsAny<SupportQuestion>(), default), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(default), Times.Once);
    }

    [Fact]
    public async Task UpdateStatusAsync_ShouldThrowNotFoundException_WhenSupportQuestionNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var status = ApplicationConstants.SupportQuestionStatus.InProgress;
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync((SupportQuestion?)null);

        // Act & Assert
        await _service.Invoking(s => s.UpdateStatusAsync(id, status, updatedBy))
            .Should().ThrowAsync<NotFoundException>();
    }

    [Fact]
    public async Task UpdateStatusAsync_ShouldThrowValidationException_WithInvalidStatus()
    {
        // Arrange
        var id = Guid.NewGuid();
        var supportQuestion = new SupportQuestion { Id = id };
        var invalidStatus = "InvalidStatus";
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync(supportQuestion);

        // Act & Assert
        await _service.Invoking(s => s.UpdateStatusAsync(id, invalidStatus, updatedBy))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task AssignToUserAsync_ShouldAssignUser_WhenBothExist()
    {
        // Arrange
        var supportQuestionId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var supportQuestion = new SupportQuestion 
        { 
            Id = supportQuestionId,
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending
        };
        var user = new User { Id = userId };
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(supportQuestionId, default))
            .ReturnsAsync(supportQuestion);

        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId, default))
            .ReturnsAsync(user);

        _mockSupportQuestionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SupportQuestion>(), default))
            .ReturnsAsync((SupportQuestion sq, CancellationToken _) => sq);

        // Act
        var result = await _service.AssignToUserAsync(supportQuestionId, userId, updatedBy);

        // Assert
        result.Should().NotBeNull();
        result.AssignedToUserId.Should().Be(userId);
        result.ProcessingStatus.Should().Be(ApplicationConstants.SupportQuestionStatus.InProgress);
        result.UpdatedBy.Should().Be(updatedBy);
        result.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task AssignToUserAsync_ShouldUnassignUser_WhenUserIdIsNull()
    {
        // Arrange
        var supportQuestionId = Guid.NewGuid();
        var supportQuestion = new SupportQuestion { Id = supportQuestionId };
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(supportQuestionId, default))
            .ReturnsAsync(supportQuestion);

        _mockSupportQuestionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SupportQuestion>(), default))
            .ReturnsAsync((SupportQuestion sq, CancellationToken _) => sq);

        // Act
        var result = await _service.AssignToUserAsync(supportQuestionId, null, updatedBy);

        // Assert
        result.Should().NotBeNull();
        result.AssignedToUserId.Should().BeNull();
        result.UpdatedBy.Should().Be(updatedBy);
        result.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task AssignToUserAsync_ShouldThrowNotFoundException_WhenUserNotExists()
    {
        // Arrange
        var supportQuestionId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var supportQuestion = new SupportQuestion { Id = supportQuestionId };
        var updatedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(supportQuestionId, default))
            .ReturnsAsync(supportQuestion);

        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId, default))
            .ReturnsAsync((User?)null);

        // Act & Assert
        await _service.Invoking(s => s.AssignToUserAsync(supportQuestionId, userId, updatedBy))
            .Should().ThrowAsync<NotFoundException>();
    }

    [Fact]
    public async Task DeleteAsync_ShouldDeleteSupportQuestion_WhenExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var supportQuestion = new SupportQuestion { Id = id };
        var deletedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync(supportQuestion);

        // Act
        await _service.DeleteAsync(id, deletedBy);

        // Assert
        _mockSupportQuestionRepository.Verify(x => x.DeleteAsync(id, default), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrowNotFoundException_WhenNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var deletedBy = "<EMAIL>";

        _mockSupportQuestionRepository
            .Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync((SupportQuestion?)null);

        // Act & Assert
        await _service.Invoking(s => s.DeleteAsync(id, deletedBy))
            .Should().ThrowAsync<NotFoundException>();
    }

    [Fact]
    public async Task GetByStatusAsync_ShouldReturnSupportQuestionsWithStatus()
    {
        // Arrange
        var status = ApplicationConstants.SupportQuestionStatus.Pending;
        var supportQuestions = new List<SupportQuestion>
        {
            new() { ProcessingStatus = status },
            new() { ProcessingStatus = status }
        };

        _mockSupportQuestionRepository
            .Setup(x => x.GetByStatusAsync(status, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _service.GetByStatusAsync(status);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.ProcessingStatus == status);
    }

    [Fact]
    public async Task GetByAssignedUserAsync_ShouldReturnAssignedSupportQuestions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var supportQuestions = new List<SupportQuestion>
        {
            new() { AssignedToUserId = userId },
            new() { AssignedToUserId = userId }
        };

        _mockSupportQuestionRepository
            .Setup(x => x.GetByAssignedUserAsync(userId, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _service.GetByAssignedUserAsync(userId);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.AssignedToUserId == userId);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnSupportQuestionsFromEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var supportQuestions = new List<SupportQuestion>
        {
            new() { Email = email },
            new() { Email = email }
        };

        _mockSupportQuestionRepository
            .Setup(x => x.GetByEmailAsync(email, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _service.GetByEmailAsync(email);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.Email == email);
    }
}
