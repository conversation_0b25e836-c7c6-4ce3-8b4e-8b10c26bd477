using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using AMS.Infrastructure.Services;
using AMS.Core.Tests.Helpers;

namespace AMS.Infrastructure.Tests.Services;

public class JwtTokenServiceTests
{
    private readonly JwtTokenService _tokenService;
    private readonly IConfiguration _configuration;
    private readonly string _secretKey = "test-secret-key-32-chars-minimum-for-jwt-signing";
    private readonly string _issuer = "AMS-Test";
    private readonly string _audience = "AMS-Test";

    public JwtTokenServiceTests()
    {
        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["JwtSettings:SecretKey"] = _secretKey,
            ["JwtSettings:Issuer"] = _issuer,
            ["JwtSettings:Audience"] = _audience,
            ["JwtSettings:ExpirationMinutes"] = "60"
        });
        
        _configuration = configurationBuilder.Build();
        _tokenService = new JwtTokenService(_configuration);
    }

    [Fact]
    public void GenerateToken_WithValidUser_ShouldReturnValidToken()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        var token = _tokenService.GenerateToken(user);

        // Assert
        token.Should().NotBeNullOrEmpty();
        
        // Verify token structure
        var tokenHandler = new JwtSecurityTokenHandler();
        tokenHandler.CanReadToken(token).Should().BeTrue();
    }

    [Fact]
    public void GenerateToken_ShouldIncludeCorrectClaims()
    {
        // Arrange
        var user = TestDataBuilder.CreateAdminUser("<EMAIL>");

        // Act
        var token = _tokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        // Check standard claims
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.NameIdentifier && c.Value == user.Id.ToString());
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Email && c.Value == user.Email);
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Name && c.Value == user.FullName);
        jsonToken.Claims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == user.Role);
        
        // Check custom claims
        jsonToken.Claims.Should().Contain(c => c.Type == "firstName" && c.Value == user.FirstName);
        jsonToken.Claims.Should().Contain(c => c.Type == "lastName" && c.Value == user.LastName);
        jsonToken.Claims.Should().Contain(c => c.Type == "isActive" && c.Value == user.IsActive.ToString().ToLower());
    }

    [Fact]
    public void GenerateToken_ShouldHaveCorrectIssuerAndAudience()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        var token = _tokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        jsonToken.Issuer.Should().Be(_issuer);
        jsonToken.Audiences.Should().Contain(_audience);
    }

    [Fact]
    public void GenerateToken_ShouldHaveCorrectExpiration()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        var beforeGeneration = DateTime.UtcNow;

        // Act
        var token = _tokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        var expectedExpiration = beforeGeneration.AddMinutes(60);
        jsonToken.ValidTo.Should().BeCloseTo(expectedExpiration, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public void GenerateToken_ShouldBeValidatableWithCorrectKey()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        var token = _tokenService.GenerateToken(user);

        // Act
        var tokenHandler = new JwtSecurityTokenHandler();
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_secretKey)),
            ValidateIssuer = true,
            ValidIssuer = _issuer,
            ValidateAudience = true,
            ValidAudience = _audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };

        // Assert
        var validateAction = () => tokenHandler.ValidateToken(token, validationParameters, out _);
        validateAction.Should().NotThrow();
    }

    [Fact]
    public void GenerateToken_WithDifferentRoles_ShouldIncludeCorrectRoleClaims()
    {
        // Arrange
        var adminUser = TestDataBuilder.CreateAdminUser();
        var managerUser = TestDataBuilder.CreateManagerUser();
        var regularUser = TestDataBuilder.CreateRegularUser();

        // Act
        var adminToken = _tokenService.GenerateToken(adminUser);
        var managerToken = _tokenService.GenerateToken(managerUser);
        var regularToken = _tokenService.GenerateToken(regularUser);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        
        var adminClaims = tokenHandler.ReadJwtToken(adminToken).Claims;
        var managerClaims = tokenHandler.ReadJwtToken(managerToken).Claims;
        var regularClaims = tokenHandler.ReadJwtToken(regularToken).Claims;

        adminClaims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == "Administrator");
        managerClaims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == "Manager");
        regularClaims.Should().Contain(c => c.Type == ClaimTypes.Role && c.Value == "User");
    }

    [Fact]
    public void GenerateToken_WithInactiveUser_ShouldIncludeInactiveStatus()
    {
        // Arrange
        var inactiveUser = TestDataBuilder.CreateInactiveUser();

        // Act
        var token = _tokenService.GenerateToken(inactiveUser);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        jsonToken.Claims.Should().Contain(c => c.Type == "isActive" && c.Value == "false");
    }

    [Fact]
    public void GenerateToken_ShouldHaveUniqueJti()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        var token1 = _tokenService.GenerateToken(user);
        var token2 = _tokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken1 = tokenHandler.ReadJwtToken(token1);
        var jsonToken2 = tokenHandler.ReadJwtToken(token2);

        var jti1 = jsonToken1.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
        var jti2 = jsonToken2.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;

        jti1.Should().NotBeNullOrEmpty();
        jti2.Should().NotBeNullOrEmpty();
        jti1.Should().NotBe(jti2);
    }

    [Fact]
    public void GenerateToken_ShouldIncludeIssuedAtClaim()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        var beforeGeneration = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        // Act
        var token = _tokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        var iatClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Iat)?.Value;
        iatClaim.Should().NotBeNullOrEmpty();
        
        var issuedAt = long.Parse(iatClaim!);
        issuedAt.Should().BeGreaterOrEqualTo(beforeGeneration);
    }

    [Fact]
    public void GenerateToken_WithNullUser_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _tokenService.GenerateToken(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void GenerateToken_WithCustomExpirationTime_ShouldRespectConfiguration()
    {
        // Arrange
        var customConfigurationBuilder = new ConfigurationBuilder();
        customConfigurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["JwtSettings:SecretKey"] = _secretKey,
            ["JwtSettings:Issuer"] = _issuer,
            ["JwtSettings:Audience"] = _audience,
            ["JwtSettings:ExpirationMinutes"] = "30" // Custom expiration
        });
        
        var customConfiguration = customConfigurationBuilder.Build();
        var customTokenService = new JwtTokenService(customConfiguration);
        var user = TestDataBuilder.CreateValidUser();
        var beforeGeneration = DateTime.UtcNow;

        // Act
        var token = customTokenService.GenerateToken(user);

        // Assert
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        var expectedExpiration = beforeGeneration.AddMinutes(30);
        jsonToken.ValidTo.Should().BeCloseTo(expectedExpiration, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public void GenerateToken_ShouldCreateDifferentTokensForSameUser()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        var token1 = _tokenService.GenerateToken(user);
        Thread.Sleep(1000); // Ensure different timestamps
        var token2 = _tokenService.GenerateToken(user);

        // Assert
        token1.Should().NotBe(token2);
        
        // But should have same user claims
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken1 = tokenHandler.ReadJwtToken(token1);
        var jsonToken2 = tokenHandler.ReadJwtToken(token2);

        var userId1 = jsonToken1.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        var userId2 = jsonToken2.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        
        userId1.Should().Be(userId2);
    }
}
