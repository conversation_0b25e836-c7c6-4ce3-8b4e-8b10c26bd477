using FluentAssertions;
using Moq;
using AMS.Core.Entities;
using AMS.Core.Interfaces;
using AMS.Infrastructure.Services;
using AMS.Core.Tests.Helpers;

namespace AMS.Infrastructure.Tests.Services;

public class UserServiceSimpleTests
{
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IPasswordService> _mockPasswordService;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly UserService _userService;

    public UserServiceSimpleTests()
    {
        _mockUserRepository = new Mock<IUserRepository>();
        _mockPasswordService = new Mock<IPasswordService>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _userService = new UserService(_mockUserRepository.Object, _mockPasswordService.Object, _mockUnitOfWork.Object);
    }

    [Fact]
    public async Task GetUserByIdAsync_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var user = TestDataBuilder.CreateValidUser();
        user.Id = userId;

        _mockUserRepository
            .Setup(repo => repo.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _userService.GetUserByIdAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(userId);
        _mockUserRepository.Verify(repo => repo.GetByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetUserByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var invalidId = Guid.NewGuid();
        _mockUserRepository
            .Setup(repo => repo.GetByIdAsync(invalidId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _userService.GetUserByIdAsync(invalidId);

        // Assert
        result.Should().BeNull();
        _mockUserRepository.Verify(repo => repo.GetByIdAsync(invalidId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetUserByEmailAsync_WithValidEmail_ShouldReturnUser()
    {
        // Arrange
        var email = "<EMAIL>";
        var user = TestDataBuilder.CreateValidUser(email: email);

        _mockUserRepository
            .Setup(repo => repo.GetByEmailAsync(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _userService.GetUserByEmailAsync(email);

        // Assert
        result.Should().NotBeNull();
        result!.Email.Should().Be(email);
        _mockUserRepository.Verify(repo => repo.GetByEmailAsync(email, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllUsersAsync_ShouldReturnAllUsers()
    {
        // Arrange
        var users = TestDataBuilder.CreateUsersWithDifferentRoles();
        _mockUserRepository
            .Setup(repo => repo.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        // Act
        var result = await _userService.GetAllUsersAsync();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().HaveCount(users.Count);
        _mockUserRepository.Verify(repo => repo.GetAllAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnOnlyActiveUsers()
    {
        // Arrange
        var activeUsers = new List<User>
        {
            TestDataBuilder.CreateValidUser(isActive: true),
            TestDataBuilder.CreateAdminUser()
        };
        
        _mockUserRepository
            .Setup(repo => repo.GetActiveUsersAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeUsers);

        // Act
        var result = await _userService.GetActiveUsersAsync();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(u => u.IsActive);
        _mockUserRepository.Verify(repo => repo.GetActiveUsersAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EmailExistsAsync_WithExistingEmail_ShouldReturnTrue()
    {
        // Arrange
        var email = "<EMAIL>";
        _mockUserRepository
            .Setup(repo => repo.EmailExistsAsync(email, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _userService.EmailExistsAsync(email);

        // Assert
        result.Should().BeTrue();
        _mockUserRepository.Verify(repo => repo.EmailExistsAsync(email, null, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EmailExistsAsync_WithNonExistingEmail_ShouldReturnFalse()
    {
        // Arrange
        var email = "<EMAIL>";
        _mockUserRepository
            .Setup(repo => repo.EmailExistsAsync(email, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _userService.EmailExistsAsync(email);

        // Assert
        result.Should().BeFalse();
        _mockUserRepository.Verify(repo => repo.EmailExistsAsync(email, null, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void UserService_ShouldBeInstantiatedCorrectly()
    {
        // Assert
        _userService.Should().NotBeNull();
        _userService.Should().BeOfType<UserService>();
    }

    [Fact]
    public async Task SearchUsersAsync_WithSearchTerm_ShouldReturnMatchingUsers()
    {
        // Arrange
        var searchTerm = "john";
        var matchingUsers = new List<User>
        {
            TestDataBuilder.CreateValidUser("John", "Doe", "<EMAIL>"),
            TestDataBuilder.CreateValidUser("Bob", "Johnson", "<EMAIL>")
        };

        _mockUserRepository
            .Setup(repo => repo.SearchUsersAsync(searchTerm, It.IsAny<CancellationToken>()))
            .ReturnsAsync(matchingUsers);

        // Act
        var result = await _userService.SearchUsersAsync(searchTerm);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().HaveCount(2);
        _mockUserRepository.Verify(repo => repo.SearchUsersAsync(searchTerm, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetUsersByRoleAsync_ShouldReturnUsersWithSpecificRole()
    {
        // Arrange
        var role = "Administrator";
        var adminUsers = new List<User>
        {
            TestDataBuilder.CreateAdminUser("<EMAIL>"),
            TestDataBuilder.CreateAdminUser("<EMAIL>")
        };

        _mockUserRepository
            .Setup(repo => repo.GetUsersByRoleAsync(role, It.IsAny<CancellationToken>()))
            .ReturnsAsync(adminUsers);

        // Act
        var result = await _userService.GetUsersByRoleAsync(role);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(u => u.Role == role);
        result.Should().HaveCount(2);
        _mockUserRepository.Verify(repo => repo.GetUsersByRoleAsync(role, It.IsAny<CancellationToken>()), Times.Once);
    }
}
