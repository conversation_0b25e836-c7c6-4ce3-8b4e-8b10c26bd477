using FluentAssertions;
using AMS.Infrastructure.Services;

namespace AMS.Infrastructure.Tests.Services;

public class PasswordServiceTests
{
    private readonly PasswordService _passwordService;

    public PasswordServiceTests()
    {
        _passwordService = new PasswordService();
    }

    [Fact]
    public void HashPassword_WithValidPassword_ShouldReturnHashedPassword()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hashedPassword = _passwordService.HashPassword(password);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Should().NotBe(password);
        hashedPassword.Length.Should().BeGreaterThan(password.Length);
    }

    [Fact]
    public void HashPassword_WithSamePassword_ShouldReturnDifferentHashes()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hash1 = _passwordService.HashPassword(password);
        var hash2 = _passwordService.HashPassword(password);

        // Assert
        hash1.Should().NotBe(hash2);
        hash1.Should().NotBeNullOrEmpty();
        hash2.Should().NotBeNullOrEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void HashPassword_WithInvalidPassword_ShouldThrowException(string? password)
    {
        // Act & Assert
        var action = () => _passwordService.HashPassword(password!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void VerifyPassword_WithCorrectPassword_ShouldReturnTrue()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordService.HashPassword(password);

        // Act
        var result = _passwordService.VerifyPassword(password, hashedPassword);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_WithIncorrectPassword_ShouldReturnFalse()
    {
        // Arrange
        var correctPassword = "TestPassword123!";
        var incorrectPassword = "WrongPassword123!";
        var hashedPassword = _passwordService.HashPassword(correctPassword);

        // Act
        var result = _passwordService.VerifyPassword(incorrectPassword, hashedPassword);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void VerifyPassword_WithCaseSensitivePassword_ShouldReturnFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var wrongCasePassword = "testpassword123!";
        var hashedPassword = _passwordService.HashPassword(password);

        // Act
        var result = _passwordService.VerifyPassword(wrongCasePassword, hashedPassword);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("", "validhash")]
    [InlineData(" ", "validhash")]
    [InlineData(null, "validhash")]
    [InlineData("validpassword", "")]
    [InlineData("validpassword", " ")]
    [InlineData("validpassword", null)]
    public void VerifyPassword_WithInvalidInputs_ShouldThrowException(string? password, string? hashedPassword)
    {
        // Act & Assert
        var action = () => _passwordService.VerifyPassword(password!, hashedPassword!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void VerifyPassword_WithInvalidHashFormat_ShouldReturnFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var invalidHash = "this-is-not-a-valid-bcrypt-hash";

        // Act
        var result = _passwordService.VerifyPassword(password, invalidHash);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HashPassword_ShouldProducePBKDF2CompatibleHash()
    {
        // Arrange
        var password = "TestPassword123!";

        // Act
        var hashedPassword = _passwordService.HashPassword(password);

        // Assert
        // PBKDF2 with Base64 encoding produces 64-character strings
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Length.Should().Be(64); // Base64 encoded (16+32 bytes = 48 bytes -> 64 chars)

        // Should be valid Base64
        var act = () => Convert.FromBase64String(hashedPassword);
        act.Should().NotThrow();

        // Decoded bytes should be 48 bytes (16 salt + 32 hash)
        var decodedBytes = Convert.FromBase64String(hashedPassword);
        decodedBytes.Length.Should().Be(48);
    }

    [Fact]
    public void HashPassword_WithLongPassword_ShouldHandleCorrectly()
    {
        // Arrange
        var longPassword = new string('a', 1000); // Very long password

        // Act
        var hashedPassword = _passwordService.HashPassword(longPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        hashedPassword.Length.Should().Be(64); // PBKDF2 Base64 always produces 64-character hash

        // Verify it can be verified
        var verificationResult = _passwordService.VerifyPassword(longPassword, hashedPassword);
        verificationResult.Should().BeTrue();
    }

    [Fact]
    public void HashPassword_WithSpecialCharacters_ShouldHandleCorrectly()
    {
        // Arrange
        var passwordWithSpecialChars = "P@ssw0rd!@#$%^&*()_+-=[]{}|;':\",./<>?`~";

        // Act
        var hashedPassword = _passwordService.HashPassword(passwordWithSpecialChars);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        
        // Verify it can be verified
        var verificationResult = _passwordService.VerifyPassword(passwordWithSpecialChars, hashedPassword);
        verificationResult.Should().BeTrue();
    }

    [Fact]
    public void HashPassword_WithUnicodeCharacters_ShouldHandleCorrectly()
    {
        // Arrange
        var unicodePassword = "Pässwörd123!こんにちは";

        // Act
        var hashedPassword = _passwordService.HashPassword(unicodePassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        
        // Verify it can be verified
        var verificationResult = _passwordService.VerifyPassword(unicodePassword, hashedPassword);
        verificationResult.Should().BeTrue();
    }

    [Fact]
    public void PasswordService_ShouldBeThreadSafe()
    {
        // Arrange
        var password = "TestPassword123!";
        var tasks = new List<Task<(string hash, bool verified)>>();

        // Act
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                var hash = _passwordService.HashPassword(password);
                var verified = _passwordService.VerifyPassword(password, hash);
                return (hash, verified);
            }));
        }

        var results = Task.WhenAll(tasks).Result;

        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r.verified);
        results.Select(r => r.hash).Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public void HashPassword_PerformanceTest_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var password = "TestPassword123!";
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var hashedPassword = _passwordService.HashPassword(password);
        stopwatch.Stop();

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
    }

    [Fact]
    public void VerifyPassword_PerformanceTest_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordService.HashPassword(password);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = _passwordService.VerifyPassword(password, hashedPassword);
        stopwatch.Stop();

        // Assert
        result.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
    }

    [Fact]
    public void HashPassword_WithMinimumLengthPassword_ShouldWork()
    {
        // Arrange
        var minimumPassword = "a"; // Single character

        // Act
        var hashedPassword = _passwordService.HashPassword(minimumPassword);

        // Assert
        hashedPassword.Should().NotBeNullOrEmpty();
        
        // Verify it can be verified
        var verificationResult = _passwordService.VerifyPassword(minimumPassword, hashedPassword);
        verificationResult.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_WithTamperedHash_ShouldReturnFalse()
    {
        // Arrange
        var password = "TestPassword123!";
        var hashedPassword = _passwordService.HashPassword(password);
        var tamperedHash = hashedPassword.Substring(0, hashedPassword.Length - 1) + "X"; // Change last character

        // Act
        var result = _passwordService.VerifyPassword(password, tamperedHash);

        // Assert
        result.Should().BeFalse();
    }
}
