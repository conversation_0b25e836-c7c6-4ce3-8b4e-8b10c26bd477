using AMS.Core.Constants;
using AMS.Core.Entities;
using AMS.Infrastructure.Data;
using AMS.Infrastructure.Repositories;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace AMS.Infrastructure.Tests.Repositories;

public class SupportQuestionRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly SupportQuestionRepository _repository;

    public SupportQuestionRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _repository = new SupportQuestionRepository(_context);
    }

    [Fact]
    public async Task GetByStatusAsync_ShouldReturnSupportQuestionsWithSpecificStatus()
    {
        // Arrange
        var pendingQuestions = new List<SupportQuestion>
        {
            new() { Name = "<PERSON>", Email = "<EMAIL>", Body = "Question 1", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending },
            new() { Name = "Jane", Email = "<EMAIL>", Body = "Question 2", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending }
        };

        var inProgressQuestion = new SupportQuestion
        {
            Name = "Bob",
            Email = "<EMAIL>",
            Body = "Question 3",
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress
        };

        await _context.SupportQuestions.AddRangeAsync(pendingQuestions);
        await _context.SupportQuestions.AddAsync(inProgressQuestion);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByStatusAsync(ApplicationConstants.SupportQuestionStatus.Pending);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.ProcessingStatus == ApplicationConstants.SupportQuestionStatus.Pending);
        result.Should().BeInDescendingOrder(sq => sq.CreatedAt);
    }

    [Fact]
    public async Task GetByAssignedUserAsync_ShouldReturnSupportQuestionsAssignedToUser()
    {
        // Arrange
        var user = new User { FirstName = "Test", LastName = "User", Email = "<EMAIL>" };
        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();

        var assignedQuestions = new List<SupportQuestion>
        {
            new() { Name = "John", Email = "<EMAIL>", Body = "Question 1", AssignedToUserId = user.Id },
            new() { Name = "Jane", Email = "<EMAIL>", Body = "Question 2", AssignedToUserId = user.Id }
        };

        var unassignedQuestion = new SupportQuestion
        {
            Name = "Bob",
            Email = "<EMAIL>",
            Body = "Question 3"
        };

        await _context.SupportQuestions.AddRangeAsync(assignedQuestions);
        await _context.SupportQuestions.AddAsync(unassignedQuestion);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByAssignedUserAsync(user.Id);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.AssignedToUserId == user.Id);
        result.Should().BeInDescendingOrder(sq => sq.CreatedAt);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnSupportQuestionsFromSpecificEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var questionsFromEmail = new List<SupportQuestion>
        {
            new() { Name = "John", Email = email, Body = "Question 1" },
            new() { Name = "John", Email = email, Body = "Question 2" }
        };

        var questionFromOtherEmail = new SupportQuestion
        {
            Name = "Jane",
            Email = "<EMAIL>",
            Body = "Question 3"
        };

        await _context.SupportQuestions.AddRangeAsync(questionsFromEmail);
        await _context.SupportQuestions.AddAsync(questionFromOtherEmail);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEmailAsync(email);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.Email.ToLower() == email.ToLower());
        result.Should().BeInDescendingOrder(sq => sq.CreatedAt);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldBeCaseInsensitive()
    {
        // Arrange
        var email = "<EMAIL>";
        var supportQuestion = new SupportQuestion
        {
            Name = "John",
            Email = email.ToUpperInvariant(),
            Body = "Question 1"
        };

        await _context.SupportQuestions.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEmailAsync(email.ToLowerInvariant());

        // Assert
        result.Should().HaveCount(1);
        result.First().Email.Should().Be(email.ToUpperInvariant());
    }

    [Fact]
    public async Task GetPagedAsync_ShouldReturnPagedResults()
    {
        // Arrange
        var supportQuestions = new List<SupportQuestion>();
        for (int i = 0; i < 10; i++)
        {
            supportQuestions.Add(new SupportQuestion
            {
                Name = $"User {i}",
                Email = $"user{i}@example.com",
                Body = $"Question {i}",
                CreatedAt = DateTime.UtcNow.AddMinutes(-i) // Different creation times for ordering
            });
        }

        await _context.SupportQuestions.AddRangeAsync(supportQuestions);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPagedAsync(skip: 2, take: 3);

        // Assert
        result.Should().HaveCount(3);
        result.Should().BeInDescendingOrder(sq => sq.CreatedAt);
    }

    [Fact]
    public async Task GetPagedAsync_WithFilters_ShouldReturnFilteredResults()
    {
        // Arrange
        var user = new User { FirstName = "Test", LastName = "User", Email = "<EMAIL>" };
        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();

        var supportQuestions = new List<SupportQuestion>
        {
            new() { Name = "John", Email = "<EMAIL>", Body = "Question 1", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending },
            new() { Name = "Jane", Email = "<EMAIL>", Body = "Question 2", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress, AssignedToUserId = user.Id },
            new() { Name = "Bob", Email = "<EMAIL>", Body = "Question 3", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending },
            new() { Name = "Alice", Email = "<EMAIL>", Body = "Question 4", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Resolved }
        };

        await _context.SupportQuestions.AddRangeAsync(supportQuestions);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPagedAsync(
            status: ApplicationConstants.SupportQuestionStatus.Pending,
            skip: 0,
            take: 10);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(sq => sq.ProcessingStatus == ApplicationConstants.SupportQuestionStatus.Pending);
    }

    [Fact]
    public async Task GetCountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        var supportQuestions = new List<SupportQuestion>
        {
            new() { Name = "John", Email = "<EMAIL>", Body = "Question 1", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending },
            new() { Name = "Jane", Email = "<EMAIL>", Body = "Question 2", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress },
            new() { Name = "Bob", Email = "<EMAIL>", Body = "Question 3", ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending }
        };

        await _context.SupportQuestions.AddRangeAsync(supportQuestions);
        await _context.SaveChangesAsync();

        // Act
        var totalCount = await _repository.GetCountAsync();
        var pendingCount = await _repository.GetCountAsync(status: ApplicationConstants.SupportQuestionStatus.Pending);

        // Assert
        totalCount.Should().Be(3);
        pendingCount.Should().Be(2);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldIncludeAssignedUser()
    {
        // Arrange
        var user = new User { FirstName = "Test", LastName = "User", Email = "<EMAIL>" };
        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();

        var supportQuestion = new SupportQuestion
        {
            Name = "John",
            Email = "<EMAIL>",
            Body = "Question 1",
            AssignedToUserId = user.Id
        };

        await _context.SupportQuestions.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(supportQuestion.Id);

        // Assert
        result.Should().NotBeNull();
        result!.AssignedToUser.Should().NotBeNull();
        result.AssignedToUser!.FirstName.Should().Be("Test");
        result.AssignedToUser.LastName.Should().Be("User");
    }

    [Fact]
    public async Task GetAllAsync_ShouldIncludeAssignedUser()
    {
        // Arrange
        var user = new User { FirstName = "Test", LastName = "User", Email = "<EMAIL>" };
        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();

        var supportQuestion = new SupportQuestion
        {
            Name = "John",
            Email = "<EMAIL>",
            Body = "Question 1",
            AssignedToUserId = user.Id
        };

        await _context.SupportQuestions.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(1);
        result.First().AssignedToUser.Should().NotBeNull();
        result.First().AssignedToUser!.FirstName.Should().Be("Test");
    }

    [Fact]
    public async Task AddAsync_ShouldAddSupportQuestion()
    {
        // Arrange
        var supportQuestion = new SupportQuestion
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Body = "I need help with my account"
        };

        // Act
        var result = await _repository.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeEmpty();
        
        var savedQuestion = await _context.SupportQuestions.FindAsync(result.Id);
        savedQuestion.Should().NotBeNull();
        savedQuestion!.Name.Should().Be("John Doe");
        savedQuestion.Email.Should().Be("<EMAIL>");
        savedQuestion.Body.Should().Be("I need help with my account");
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateSupportQuestion()
    {
        // Arrange
        var supportQuestion = new SupportQuestion
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Body = "Original question",
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending
        };

        await _context.SupportQuestions.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Act
        supportQuestion.ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress;
        supportQuestion.UpdatedAt = DateTime.UtcNow;
        supportQuestion.UpdatedBy = "<EMAIL>";

        var result = await _repository.UpdateAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Assert
        result.Should().NotBeNull();
        result.ProcessingStatus.Should().Be(ApplicationConstants.SupportQuestionStatus.InProgress);
        result.UpdatedAt.Should().NotBeNull();
        result.UpdatedBy.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task DeleteAsync_ShouldSoftDeleteSupportQuestion()
    {
        // Arrange
        var supportQuestion = new SupportQuestion
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Body = "Question to be deleted"
        };

        await _context.SupportQuestions.AddAsync(supportQuestion);
        await _context.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(supportQuestion.Id);
        await _context.SaveChangesAsync();

        // Assert
        var deletedQuestion = await _context.SupportQuestions
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(sq => sq.Id == supportQuestion.Id);
        
        deletedQuestion.Should().NotBeNull();
        deletedQuestion!.IsDeleted.Should().BeTrue();

        // Verify it's not returned by normal queries (due to soft delete filter)
        var normalQuery = await _repository.GetByIdAsync(supportQuestion.Id);
        normalQuery.Should().BeNull();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
