using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using AMS.Core.Entities;
using AMS.Infrastructure.Repositories;
using AMS.Infrastructure.Tests.Helpers;
using AMS.Core.Tests.Helpers;

namespace AMS.Infrastructure.Tests.Repositories;

public class UserRepositorySimpleTests : DatabaseTestBase
{
    private UserRepository _repository;

    public UserRepositorySimpleTests()
    {
        _repository = new UserRepository(Context);
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(user.Id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(user.Id);
        result.Email.Should().Be(user.Email);
    }

    [Fact]
    public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var result = await _repository.GetByIdAsync(invalidId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByEmailAsync_WithValidEmail_ShouldReturnUser()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser(email: "<EMAIL>");
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEmailAsync("<EMAIL>");

        // Assert
        result.Should().NotBeNull();
        result!.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GetByEmailAsync_WithInvalidEmail_ShouldReturnNull()
    {
        // Act
        var result = await _repository.GetByEmailAsync("<EMAIL>");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllActiveUsers()
    {
        // Arrange
        var user1 = TestDataBuilder.CreateValidUser("John", "Doe", "<EMAIL>");
        var user2 = TestDataBuilder.CreateValidUser("Jane", "Smith", "<EMAIL>");
        var deletedUser = TestDataBuilder.CreateDeletedUser();
        
        Context.Users.AddRange(user1, user2, deletedUser);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().NotContain(u => u.IsDeleted);
        result.Count().Should().Be(2); // Excluding deleted user
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnOnlyActiveUsers()
    {
        // Arrange
        var activeUser = TestDataBuilder.CreateValidUser(isActive: true);
        var inactiveUser = TestDataBuilder.CreateInactiveUser();
        
        Context.Users.AddRange(activeUser, inactiveUser);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveUsersAsync();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(u => u.IsActive);
        result.Should().NotContain(u => u.Id == inactiveUser.Id);
    }

    [Fact]
    public async Task EmailExistsAsync_WithExistingEmail_ShouldReturnTrue()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser(email: "<EMAIL>");
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.EmailExistsAsync("<EMAIL>");

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task EmailExistsAsync_WithNonExistingEmail_ShouldReturnFalse()
    {
        // Act
        var result = await _repository.EmailExistsAsync("<EMAIL>");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task AddAsync_ShouldAddUserToDatabase()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        await _repository.AddAsync(user);
        await Context.SaveChangesAsync();

        // Assert
        var addedUser = await Context.Users.FindAsync(user.Id);
        addedUser.Should().NotBeNull();
        addedUser!.Email.Should().Be(user.Email);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateUserInDatabase()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        user.FirstName = "Updated";
        await _repository.UpdateAsync(user);
        await Context.SaveChangesAsync();

        // Assert
        var updatedUser = await Context.Users.FindAsync(user.Id);
        updatedUser.Should().NotBeNull();
        updatedUser!.FirstName.Should().Be("Updated");
    }

    [Fact]
    public async Task DeleteAsync_ShouldSoftDeleteUser()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(user.Id);
        await Context.SaveChangesAsync();

        // Assert
        var deletedUser = await Context.Users.IgnoreQueryFilters().FirstOrDefaultAsync(u => u.Id == user.Id);
        deletedUser.Should().NotBeNull();
        deletedUser!.IsDeleted.Should().BeTrue();
        
        // Verify it's not returned by normal queries
        var normalQuery = await _repository.GetByIdAsync(user.Id);
        normalQuery.Should().BeNull();
    }

    [Fact]
    public async Task SearchUsersAsync_ShouldReturnMatchingUsers()
    {
        // Arrange
        var user1 = TestDataBuilder.CreateValidUser("John", "Doe", "<EMAIL>");
        var user2 = TestDataBuilder.CreateValidUser("Jane", "Smith", "<EMAIL>");
        var user3 = TestDataBuilder.CreateValidUser("Bob", "Johnson", "<EMAIL>");
        
        Context.Users.AddRange(user1, user2, user3);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.SearchUsersAsync("john");

        // Assert
        result.Should().NotBeEmpty();
        // Should find John Doe and Bob Johnson (contains "john" in name or email)
        result.Should().HaveCountGreaterOrEqualTo(1);
    }

    [Fact]
    public async Task ExistsAsync_WithExistingId_ShouldReturnTrue()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsAsync(user.Id);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExistsAsync_WithNonExistingId_ShouldReturnFalse()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();

        // Act
        var result = await _repository.ExistsAsync(nonExistingId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task CountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        var users = TestDataBuilder.CreateUserList(5);
        Context.Users.AddRange(users);
        await Context.SaveChangesAsync();

        // Act
        var count = await _repository.CountAsync();

        // Assert
        count.Should().Be(5);
    }

    public override void Dispose()
    {
        // UserRepository doesn't implement IDisposable, so we don't need to dispose it
        base.Dispose();
    }
}
