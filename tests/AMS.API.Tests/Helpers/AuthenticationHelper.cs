using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Microsoft.IdentityModel.Tokens;
using AMS.Core.Constants;

namespace AMS.API.Tests.Helpers;

/// <summary>
/// Helper class for authentication in tests
/// </summary>
public static class AuthenticationHelper
{
    private const string TestSecretKey = "test-secret-key-32-chars-minimum-for-jwt-signing";
    private const string TestIssuer = "AMS-Test";
    private const string TestAudience = "AMS-Test";

    /// <summary>
    /// Generates a JWT token for testing
    /// </summary>
    public static string GenerateJwtToken(
        string userId,
        string email,
        string role,
        int expirationMinutes = 60)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(TestSecretKey);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId),
            new(ClaimTypes.Email, email),
            new(ClaimTypes.Role, role),
            new("sub", userId),
            new("email", email),
            new("role", role)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(expirationMinutes),
            Issuer = TestIssuer,
            Audience = TestAudience,
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Generates a JWT token for an administrator
    /// </summary>
    public static string GenerateAdminToken()
    {
        return GenerateJwtToken(
            "11111111-1111-1111-1111-111111111111",
            "<EMAIL>",
            ApplicationConstants.Roles.Administrator
        );
    }

    /// <summary>
    /// Generates a JWT token for a manager
    /// </summary>
    public static string GenerateManagerToken()
    {
        return GenerateJwtToken(
            "22222222-2222-2222-2222-222222222222",
            "<EMAIL>",
            ApplicationConstants.Roles.Manager
        );
    }

    /// <summary>
    /// Generates a JWT token for a regular user
    /// </summary>
    public static string GenerateUserToken()
    {
        return GenerateJwtToken(
            "33333333-3333-3333-3333-333333333333",
            "<EMAIL>",
            ApplicationConstants.Roles.User
        );
    }

    /// <summary>
    /// Generates an expired JWT token for testing
    /// </summary>
    public static string GenerateExpiredToken()
    {
        return GenerateJwtToken(
            "33333333-3333-3333-3333-333333333333",
            "<EMAIL>",
            ApplicationConstants.Roles.User,
            -60 // Expired 1 hour ago
        );
    }

    /// <summary>
    /// Adds authorization header to HttpClient
    /// </summary>
    public static void AddAuthorizationHeader(HttpClient client, string token)
    {
        client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
    }

    /// <summary>
    /// Adds admin authorization to HttpClient
    /// </summary>
    public static void AddAdminAuthorization(HttpClient client)
    {
        var token = GenerateAdminToken();
        AddAuthorizationHeader(client, token);
    }

    /// <summary>
    /// Adds manager authorization to HttpClient
    /// </summary>
    public static void AddManagerAuthorization(HttpClient client)
    {
        var token = GenerateManagerToken();
        AddAuthorizationHeader(client, token);
    }

    /// <summary>
    /// Adds user authorization to HttpClient
    /// </summary>
    public static void AddUserAuthorization(HttpClient client)
    {
        var token = GenerateUserToken();
        AddAuthorizationHeader(client, token);
    }

    /// <summary>
    /// Removes authorization header from HttpClient
    /// </summary>
    public static void RemoveAuthorization(HttpClient client)
    {
        client.DefaultRequestHeaders.Authorization = null;
    }

    /// <summary>
    /// Creates a login request payload
    /// </summary>
    public static StringContent CreateLoginRequest(string email, string password)
    {
        var loginData = new
        {
            email = email,
            password = password
        };

        var json = JsonSerializer.Serialize(loginData);
        return new StringContent(json, Encoding.UTF8, "application/json");
    }

    /// <summary>
    /// Creates an admin login request
    /// </summary>
    public static StringContent CreateAdminLoginRequest()
    {
        return CreateLoginRequest("<EMAIL>", "Admin123!");
    }

    /// <summary>
    /// Creates a manager login request
    /// </summary>
    public static StringContent CreateManagerLoginRequest()
    {
        return CreateLoginRequest("<EMAIL>", "Manager123!");
    }

    /// <summary>
    /// Creates a user login request
    /// </summary>
    public static StringContent CreateUserLoginRequest()
    {
        return CreateLoginRequest("<EMAIL>", "User123!");
    }

    /// <summary>
    /// Extracts token from login response
    /// </summary>
    public static async Task<string?> ExtractTokenFromResponse(HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
            return null;

        var content = await response.Content.ReadAsStringAsync();
        using var document = JsonDocument.Parse(content);
        
        if (document.RootElement.TryGetProperty("data", out var dataElement) &&
            dataElement.TryGetProperty("accessToken", out var tokenElement))
        {
            return tokenElement.GetString();
        }

        return null;
    }

    /// <summary>
    /// Performs login and returns the JWT token
    /// </summary>
    public static async Task<string?> LoginAndGetTokenAsync(HttpClient client, string email, string password)
    {
        var loginRequest = CreateLoginRequest(email, password);
        var response = await client.PostAsync("/api/auth/login", loginRequest);
        return await ExtractTokenFromResponse(response);
    }

    /// <summary>
    /// Performs admin login and returns the JWT token
    /// </summary>
    public static async Task<string?> AdminLoginAsync(HttpClient client)
    {
        return await LoginAndGetTokenAsync(client, "<EMAIL>", "Admin123!");
    }

    /// <summary>
    /// Performs manager login and returns the JWT token
    /// </summary>
    public static async Task<string?> ManagerLoginAsync(HttpClient client)
    {
        return await LoginAndGetTokenAsync(client, "<EMAIL>", "Manager123!");
    }

    /// <summary>
    /// Performs user login and returns the JWT token
    /// </summary>
    public static async Task<string?> UserLoginAsync(HttpClient client)
    {
        return await LoginAndGetTokenAsync(client, "<EMAIL>", "User123!");
    }
}
