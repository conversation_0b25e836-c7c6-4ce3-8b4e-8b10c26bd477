using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AMS.Infrastructure.Data;
using AMS.Core.Entities;
using AMS.Core.Constants;

namespace AMS.API.Tests.Helpers;

/// <summary>
/// Custom WebApplicationFactory for integration testing
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public string DatabaseName { get; private set; } = Guid.NewGuid().ToString();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database context
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseInMemoryDatabase(DatabaseName);
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Build service provider and seed database
            var serviceProvider = services.BuildServiceProvider();
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            SeedTestDatabase(context);
        });

        builder.UseEnvironment("Testing");
    }

    /// <summary>
    /// Seeds the test database with initial data
    /// </summary>
    private static void SeedTestDatabase(ApplicationDbContext context)
    {
        // Clear existing data
        context.Users.RemoveRange(context.Users);
        context.SaveChanges();

        // Add test users
        var testUsers = new List<User>
        {
            new User
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                FirstName = "Test",
                LastName = "Admin",
                Email = "<EMAIL>",
                PasswordHash = "$2a$10$test.hash.for.admin.user.password",
                Role = ApplicationConstants.Roles.Administrator,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "TestSeeder"
            },
            new User
            {
                Id = Guid.Parse("*************-2222-2222-************"),
                FirstName = "Test",
                LastName = "Manager",
                Email = "<EMAIL>",
                PasswordHash = "$2a$10$test.hash.for.manager.user.password",
                Role = ApplicationConstants.Roles.Manager,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "TestSeeder"
            },
            new User
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                FirstName = "Test",
                LastName = "User",
                Email = "<EMAIL>",
                PasswordHash = "$2a$10$test.hash.for.regular.user.password",
                Role = ApplicationConstants.Roles.User,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "TestSeeder"
            },
            new User
            {
                Id = Guid.Parse("*************-4444-4444-************"),
                FirstName = "Inactive",
                LastName = "User",
                Email = "<EMAIL>",
                PasswordHash = "$2a$10$test.hash.for.inactive.user.password",
                Role = ApplicationConstants.Roles.User,
                IsActive = false,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "TestSeeder"
            }
        };

        context.Users.AddRange(testUsers);
        context.SaveChanges();
    }

    /// <summary>
    /// Gets a scoped database context for testing
    /// </summary>
    public ApplicationDbContext GetDbContext()
    {
        var scope = Services.CreateScope();
        return scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }

    /// <summary>
    /// Clears all data from the test database
    /// </summary>
    public void ClearDatabase()
    {
        using var context = GetDbContext();
        context.Users.RemoveRange(context.Users);
        context.SaveChanges();
    }

    /// <summary>
    /// Reseeds the test database
    /// </summary>
    public void ReseedDatabase()
    {
        using var context = GetDbContext();
        SeedTestDatabase(context);
    }
}

/// <summary>
/// Base class for API integration tests
/// </summary>
public abstract class ApiTestBase : IClassFixture<TestWebApplicationFactory>, IDisposable
{
    protected TestWebApplicationFactory Factory { get; }
    protected HttpClient Client { get; }

    protected ApiTestBase(TestWebApplicationFactory factory)
    {
        Factory = factory;
        Client = factory.CreateClient();
    }

    /// <summary>
    /// Gets a fresh database context for testing
    /// </summary>
    protected ApplicationDbContext GetDbContext()
    {
        return Factory.GetDbContext();
    }

    /// <summary>
    /// Clears the test database
    /// </summary>
    protected void ClearDatabase()
    {
        Factory.ClearDatabase();
    }

    /// <summary>
    /// Reseeds the test database
    /// </summary>
    protected void ReseedDatabase()
    {
        Factory.ReseedDatabase();
    }

    public virtual void Dispose()
    {
        Client?.Dispose();
        GC.SuppressFinalize(this);
    }
}
