using AMS.Core.Entities;
using AMS.Core.Constants;

namespace AMS.Core.Tests.Helpers;

/// <summary>
/// Builder class for creating test data objects
/// </summary>
public static class TestDataBuilder
{
    /// <summary>
    /// Creates a valid user for testing
    /// </summary>
    public static User CreateValidUser(
        string? firstName = null,
        string? lastName = null,
        string? email = null,
        string? role = null,
        bool isActive = true)
    {
        return new User
        {
            Id = Guid.NewGuid(),
            FirstName = firstName ?? "John",
            LastName = lastName ?? "Doe",
            Email = email ?? "<EMAIL>",
            PasswordHash = "hashed_password_123",
            Role = role ?? ApplicationConstants.Roles.User,
            IsActive = isActive,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "TestSystem"
        };
    }

    /// <summary>
    /// Creates an administrator user for testing
    /// </summary>
    public static User CreateAdminUser(string? email = null)
    {
        return CreateValidUser(
            firstName: "Admin",
            lastName: "User",
            email: email ?? "<EMAIL>",
            role: ApplicationConstants.Roles.Administrator
        );
    }

    /// <summary>
    /// Creates a manager user for testing
    /// </summary>
    public static User CreateManagerUser(string? email = null)
    {
        return CreateValidUser(
            firstName: "Manager",
            lastName: "User",
            email: email ?? "<EMAIL>",
            role: ApplicationConstants.Roles.Manager
        );
    }

    /// <summary>
    /// Creates a regular user for testing
    /// </summary>
    public static User CreateRegularUser(string? email = null)
    {
        return CreateValidUser(
            firstName: "Regular",
            lastName: "User",
            email: email ?? "<EMAIL>",
            role: ApplicationConstants.Roles.User
        );
    }

    /// <summary>
    /// Creates an inactive user for testing
    /// </summary>
    public static User CreateInactiveUser(string? email = null)
    {
        return CreateValidUser(
            firstName: "Inactive",
            lastName: "User",
            email: email ?? "<EMAIL>",
            isActive: false
        );
    }

    /// <summary>
    /// Creates a list of users for testing
    /// </summary>
    public static List<User> CreateUserList(int count = 5)
    {
        var users = new List<User>();
        
        for (int i = 0; i < count; i++)
        {
            users.Add(CreateValidUser(
                firstName: $"User{i}",
                lastName: $"Test{i}",
                email: $"user{i}@test.com"
            ));
        }

        return users;
    }

    /// <summary>
    /// Creates users with different roles for testing
    /// </summary>
    public static List<User> CreateUsersWithDifferentRoles()
    {
        return new List<User>
        {
            CreateAdminUser("<EMAIL>"),
            CreateManagerUser("<EMAIL>"),
            CreateRegularUser("<EMAIL>"),
            CreateRegularUser("<EMAIL>"),
            CreateInactiveUser("<EMAIL>")
        };
    }

    /// <summary>
    /// Creates a user with minimal required fields
    /// </summary>
    public static User CreateMinimalUser()
    {
        return new User
        {
            Id = Guid.NewGuid(),
            FirstName = "Min",
            LastName = "User",
            Email = "<EMAIL>",
            PasswordHash = "hash",
            Role = ApplicationConstants.Roles.User,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a user with all audit fields populated
    /// </summary>
    public static User CreateUserWithAuditFields()
    {
        var now = DateTime.UtcNow;
        return new User
        {
            Id = Guid.NewGuid(),
            FirstName = "Audit",
            LastName = "User",
            Email = "<EMAIL>",
            PasswordHash = "hashed_password",
            Role = ApplicationConstants.Roles.User,
            IsActive = true,
            CreatedAt = now.AddDays(-10),
            UpdatedAt = now.AddDays(-1),
            CreatedBy = "TestCreator",
            UpdatedBy = "TestUpdater",
            LastLoginAt = now.AddHours(-2)
        };
    }

    /// <summary>
    /// Creates a soft-deleted user for testing
    /// </summary>
    public static User CreateDeletedUser()
    {
        var user = CreateValidUser(
            firstName: "Deleted",
            lastName: "User",
            email: "<EMAIL>"
        );
        user.IsDeleted = true;
        user.UpdatedAt = DateTime.UtcNow;
        user.UpdatedBy = "TestDeleter";
        return user;
    }
}
