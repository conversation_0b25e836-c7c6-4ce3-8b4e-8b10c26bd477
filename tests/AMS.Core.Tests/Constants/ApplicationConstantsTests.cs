using FluentAssertions;
using AMS.Core.Constants;

namespace AMS.Core.Tests.Constants;

public class ApplicationConstantsTests
{
    [Fact]
    public void Roles_ShouldHaveCorrectValues()
    {
        // Assert
        ApplicationConstants.Roles.Administrator.Should().Be("Administrator");
        ApplicationConstants.Roles.Manager.Should().Be("Manager");
        ApplicationConstants.Roles.User.Should().Be("User");
    }

    [Fact]
    public void Roles_ShouldBeUniqueValues()
    {
        // Arrange
        var roles = new[]
        {
            ApplicationConstants.Roles.Administrator,
            ApplicationConstants.Roles.Manager,
            ApplicationConstants.Roles.User
        };

        // Assert
        roles.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public void Policies_ShouldHaveCorrectValues()
    {
        // Assert
        ApplicationConstants.Policies.RequireAdministratorRole.Should().Be("RequireAdministratorRole");
        ApplicationConstants.Policies.RequireManagerRole.Should().Be("RequireManagerRole");
        ApplicationConstants.Policies.RequireUserRole.Should().Be("RequireUserRole");
    }

    [Fact]
    public void Policies_ShouldBeUniqueValues()
    {
        // Arrange
        var policies = new[]
        {
            ApplicationConstants.Policies.RequireAdministratorRole,
            ApplicationConstants.Policies.RequireManagerRole,
            ApplicationConstants.Policies.RequireUserRole
        };

        // Assert
        policies.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public void Authentication_ShouldHaveCorrectValues()
    {
        // Assert
        ApplicationConstants.Authentication.MinPasswordLength.Should().Be(8);
    }

    [Fact]
    public void Authentication_ShouldHaveReasonableValues()
    {
        // Assert
        ApplicationConstants.Authentication.MinPasswordLength.Should().BeGreaterThan(0);
    }

    [Fact]
    public void Validation_ShouldHaveCorrectValues()
    {
        // Assert
        ApplicationConstants.Validation.MaxFirstNameLength.Should().Be(50);
        ApplicationConstants.Validation.MaxLastNameLength.Should().Be(50);
        ApplicationConstants.Validation.MaxEmailLength.Should().Be(100);
    }

    [Fact]
    public void Validation_ShouldHaveReasonableValues()
    {
        // Assert
        ApplicationConstants.Validation.MaxFirstNameLength.Should().BeGreaterThan(0);
        ApplicationConstants.Validation.MaxLastNameLength.Should().BeGreaterThan(0);
        ApplicationConstants.Validation.MaxEmailLength.Should().BeGreaterThan(0);
    }

    [Theory]
    [InlineData("Administrator")]
    [InlineData("Manager")]
    [InlineData("User")]
    public void Roles_ShouldContainExpectedRole(string expectedRole)
    {
        // Arrange
        var allRoles = new[]
        {
            ApplicationConstants.Roles.Administrator,
            ApplicationConstants.Roles.Manager,
            ApplicationConstants.Roles.User
        };

        // Assert
        allRoles.Should().Contain(expectedRole);
    }

    [Theory]
    [InlineData("RequireAdministratorRole")]
    [InlineData("RequireManagerRole")]
    [InlineData("RequireUserRole")]
    public void Policies_ShouldContainExpectedPolicy(string expectedPolicy)
    {
        // Arrange
        var allPolicies = new[]
        {
            ApplicationConstants.Policies.RequireAdministratorRole,
            ApplicationConstants.Policies.RequireManagerRole,
            ApplicationConstants.Policies.RequireUserRole
        };

        // Assert
        allPolicies.Should().Contain(expectedPolicy);
    }

    [Fact]
    public void Constants_ShouldNotBeNullOrEmpty()
    {
        // Assert - Roles
        ApplicationConstants.Roles.Administrator.Should().NotBeNullOrEmpty();
        ApplicationConstants.Roles.Manager.Should().NotBeNullOrEmpty();
        ApplicationConstants.Roles.User.Should().NotBeNullOrEmpty();

        // Assert - Policies
        ApplicationConstants.Policies.RequireAdministratorRole.Should().NotBeNullOrEmpty();
        ApplicationConstants.Policies.RequireManagerRole.Should().NotBeNullOrEmpty();
        ApplicationConstants.Policies.RequireUserRole.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void Constants_ShouldBeConsistentWithNaming()
    {
        // Assert - Role names should match policy suffixes
        ApplicationConstants.Policies.RequireAdministratorRole.Should().EndWith("Role");
        ApplicationConstants.Policies.RequireManagerRole.Should().EndWith("Role");
        ApplicationConstants.Policies.RequireUserRole.Should().EndWith("Role");

        // Assert - Policy names should start with "Require"
        ApplicationConstants.Policies.RequireAdministratorRole.Should().StartWith("Require");
        ApplicationConstants.Policies.RequireManagerRole.Should().StartWith("Require");
        ApplicationConstants.Policies.RequireUserRole.Should().StartWith("Require");
    }
}
