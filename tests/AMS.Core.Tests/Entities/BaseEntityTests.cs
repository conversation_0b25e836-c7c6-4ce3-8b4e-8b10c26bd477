using FluentAssertions;
using AMS.Core.Entities;

namespace AMS.Core.Tests.Entities;

public class BaseEntityTests
{
    private class TestEntity : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
    }

    [Fact]
    public void BaseEntity_ShouldHaveUniqueId()
    {
        // Arrange & Act
        var entity1 = new TestEntity();
        var entity2 = new TestEntity();

        // Assert
        entity1.Id.Should().NotBe(entity2.Id);
        entity1.Id.Should().NotBeEmpty();
        entity2.Id.Should().NotBeEmpty();
    }

    [Fact]
    public void BaseEntity_ShouldSetCreatedAtOnInstantiation()
    {
        // Arrange
        var beforeCreation = DateTime.UtcNow;

        // Act
        var entity = new TestEntity();
        var afterCreation = DateTime.UtcNow;

        // Assert
        entity.CreatedAt.Should().BeOnOrAfter(beforeCreation);
        entity.CreatedAt.Should().BeOnOrBefore(afterCreation);
    }

    [Fact]
    public void BaseEntity_ShouldHaveNullUpdatedAtInitially()
    {
        // Arrange & Act
        var entity = new TestEntity();

        // Assert
        entity.UpdatedAt.Should().BeNull();
    }

    [Fact]
    public void BaseEntity_ShouldHaveNullAuditFieldsInitially()
    {
        // Arrange & Act
        var entity = new TestEntity();

        // Assert
        entity.CreatedBy.Should().BeNull();
        entity.UpdatedBy.Should().BeNull();
    }

    [Fact]
    public void BaseEntity_ShouldNotBeDeletedInitially()
    {
        // Arrange & Act
        var entity = new TestEntity();

        // Assert
        entity.IsDeleted.Should().BeFalse();
    }

    [Fact]
    public void BaseEntity_ShouldAllowSettingAuditFields()
    {
        // Arrange
        var entity = new TestEntity();
        var createdBy = "TestCreator";
        var updatedBy = "TestUpdater";
        var updatedAt = DateTime.UtcNow;

        // Act
        entity.CreatedBy = createdBy;
        entity.UpdatedBy = updatedBy;
        entity.UpdatedAt = updatedAt;

        // Assert
        entity.CreatedBy.Should().Be(createdBy);
        entity.UpdatedBy.Should().Be(updatedBy);
        entity.UpdatedAt.Should().Be(updatedAt);
    }

    [Fact]
    public void BaseEntity_ShouldAllowSoftDelete()
    {
        // Arrange
        var entity = new TestEntity();
        entity.IsDeleted.Should().BeFalse();

        // Act
        entity.IsDeleted = true;

        // Assert
        entity.IsDeleted.Should().BeTrue();
    }

    [Fact]
    public void BaseEntity_ShouldAllowCustomId()
    {
        // Arrange
        var customId = Guid.NewGuid();
        var entity = new TestEntity();

        // Act
        entity.Id = customId;

        // Assert
        entity.Id.Should().Be(customId);
    }

    [Fact]
    public void BaseEntity_ShouldAllowCustomCreatedAt()
    {
        // Arrange
        var customCreatedAt = DateTime.UtcNow.AddDays(-1);
        var entity = new TestEntity();

        // Act
        entity.CreatedAt = customCreatedAt;

        // Assert
        entity.CreatedAt.Should().Be(customCreatedAt);
    }

    [Theory]
    [InlineData("Creator1")]
    [InlineData("System")]
    [InlineData("")]
    [InlineData(null)]
    public void BaseEntity_ShouldAcceptVariousCreatedByValues(string? createdBy)
    {
        // Arrange
        var entity = new TestEntity();

        // Act
        entity.CreatedBy = createdBy;

        // Assert
        entity.CreatedBy.Should().Be(createdBy);
    }

    [Theory]
    [InlineData("Updater1")]
    [InlineData("System")]
    [InlineData("")]
    [InlineData(null)]
    public void BaseEntity_ShouldAcceptVariousUpdatedByValues(string? updatedBy)
    {
        // Arrange
        var entity = new TestEntity();

        // Act
        entity.UpdatedBy = updatedBy;

        // Assert
        entity.UpdatedBy.Should().Be(updatedBy);
    }

    [Fact]
    public void BaseEntity_ShouldMaintainStateAfterModification()
    {
        // Arrange
        var entity = new TestEntity { Name = "Original" };
        var originalId = entity.Id;
        var originalCreatedAt = entity.CreatedAt;

        // Act
        entity.Name = "Modified";
        entity.UpdatedAt = DateTime.UtcNow;
        entity.UpdatedBy = "TestUpdater";

        // Assert
        entity.Id.Should().Be(originalId);
        entity.CreatedAt.Should().Be(originalCreatedAt);
        entity.Name.Should().Be("Modified");
        entity.UpdatedAt.Should().NotBeNull();
        entity.UpdatedBy.Should().Be("TestUpdater");
    }
}
