using AMS.Core.Constants;
using AMS.Core.Entities;
using FluentAssertions;

namespace AMS.Core.Tests.Entities;

public class SupportQuestionTests
{
    [Fact]
    public void SupportQuestion_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var supportQuestion = new SupportQuestion();

        // Assert
        supportQuestion.Id.Should().NotBeEmpty();
        supportQuestion.Name.Should().BeEmpty();
        supportQuestion.Email.Should().BeEmpty();
        supportQuestion.Body.Should().BeEmpty();
        supportQuestion.ProcessingStatus.Should().Be(ApplicationConstants.SupportQuestionStatus.Pending);
        supportQuestion.AssignedToUserId.Should().BeNull();
        supportQuestion.AssignedToUser.Should().BeNull();
        supportQuestion.IsDeleted.Should().BeFalse();
        supportQuestion.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SupportQuestion_ShouldAllowSettingProperties()
    {
        // Arrange
        var supportQuestion = new SupportQuestion();
        var name = "<PERSON>";
        var email = "<EMAIL>";
        var body = "I need help with my account";
        var status = ApplicationConstants.SupportQuestionStatus.InProgress;
        var userId = Guid.NewGuid();

        // Act
        supportQuestion.Name = name;
        supportQuestion.Email = email;
        supportQuestion.Body = body;
        supportQuestion.ProcessingStatus = status;
        supportQuestion.AssignedToUserId = userId;

        // Assert
        supportQuestion.Name.Should().Be(name);
        supportQuestion.Email.Should().Be(email);
        supportQuestion.Body.Should().Be(body);
        supportQuestion.ProcessingStatus.Should().Be(status);
        supportQuestion.AssignedToUserId.Should().Be(userId);
    }

    [Theory]
    [InlineData(ApplicationConstants.SupportQuestionStatus.Pending, true, false, false, false)]
    [InlineData(ApplicationConstants.SupportQuestionStatus.InProgress, false, true, false, false)]
    [InlineData(ApplicationConstants.SupportQuestionStatus.Resolved, false, false, true, false)]
    [InlineData(ApplicationConstants.SupportQuestionStatus.Closed, false, false, false, true)]
    public void SupportQuestion_ComputedProperties_ShouldReturnCorrectValues(
        string status, bool expectedIsPending, bool expectedIsInProgress, 
        bool expectedIsResolved, bool expectedIsClosed)
    {
        // Arrange
        var supportQuestion = new SupportQuestion
        {
            ProcessingStatus = status
        };

        // Act & Assert
        supportQuestion.IsPending.Should().Be(expectedIsPending);
        supportQuestion.IsInProgress.Should().Be(expectedIsInProgress);
        supportQuestion.IsResolved.Should().Be(expectedIsResolved);
        supportQuestion.IsClosed.Should().Be(expectedIsClosed);
    }

    [Fact]
    public void SupportQuestion_ShouldInheritFromBaseEntity()
    {
        // Arrange & Act
        var supportQuestion = new SupportQuestion();

        // Assert
        supportQuestion.Should().BeAssignableTo<BaseEntity>();
        supportQuestion.Id.Should().NotBeEmpty();
        supportQuestion.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        supportQuestion.UpdatedAt.Should().BeNull();
        supportQuestion.IsDeleted.Should().BeFalse();
    }

    [Fact]
    public void SupportQuestion_ShouldAllowSettingAuditFields()
    {
        // Arrange
        var supportQuestion = new SupportQuestion();
        var createdBy = "TestCreator";
        var updatedBy = "TestUpdater";
        var updatedAt = DateTime.UtcNow;

        // Act
        supportQuestion.CreatedBy = createdBy;
        supportQuestion.UpdatedBy = updatedBy;
        supportQuestion.UpdatedAt = updatedAt;

        // Assert
        supportQuestion.CreatedBy.Should().Be(createdBy);
        supportQuestion.UpdatedBy.Should().Be(updatedBy);
        supportQuestion.UpdatedAt.Should().Be(updatedAt);
    }

    [Fact]
    public void SupportQuestion_ShouldAllowSoftDelete()
    {
        // Arrange
        var supportQuestion = new SupportQuestion();
        supportQuestion.IsDeleted.Should().BeFalse();

        // Act
        supportQuestion.IsDeleted = true;

        // Assert
        supportQuestion.IsDeleted.Should().BeTrue();
    }

    [Fact]
    public void SupportQuestion_ShouldMaintainStateAfterModification()
    {
        // Arrange
        var supportQuestion = new SupportQuestion 
        { 
            Name = "Original Name",
            Email = "<EMAIL>",
            Body = "Original body"
        };
        var originalId = supportQuestion.Id;
        var originalCreatedAt = supportQuestion.CreatedAt;

        // Act
        supportQuestion.Name = "Modified Name";
        supportQuestion.Email = "<EMAIL>";
        supportQuestion.Body = "Modified body";
        supportQuestion.ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress;
        supportQuestion.UpdatedAt = DateTime.UtcNow;
        supportQuestion.UpdatedBy = "TestUpdater";

        // Assert
        supportQuestion.Id.Should().Be(originalId);
        supportQuestion.CreatedAt.Should().Be(originalCreatedAt);
        supportQuestion.Name.Should().Be("Modified Name");
        supportQuestion.Email.Should().Be("<EMAIL>");
        supportQuestion.Body.Should().Be("Modified body");
        supportQuestion.ProcessingStatus.Should().Be(ApplicationConstants.SupportQuestionStatus.InProgress);
        supportQuestion.UpdatedAt.Should().NotBeNull();
        supportQuestion.UpdatedBy.Should().Be("TestUpdater");
    }

    [Fact]
    public void SupportQuestion_WithAssignedUser_ShouldAllowNavigation()
    {
        // Arrange
        var user = new User
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>"
        };
        var supportQuestion = new SupportQuestion
        {
            AssignedToUserId = user.Id,
            AssignedToUser = user
        };

        // Act & Assert
        supportQuestion.AssignedToUserId.Should().Be(user.Id);
        supportQuestion.AssignedToUser.Should().NotBeNull();
        supportQuestion.AssignedToUser!.FirstName.Should().Be("John");
        supportQuestion.AssignedToUser.LastName.Should().Be("Doe");
        supportQuestion.AssignedToUser.Email.Should().Be("<EMAIL>");
    }
}
