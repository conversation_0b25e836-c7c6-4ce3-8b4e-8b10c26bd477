using FluentAssertions;
using AMS.Core.Entities;
using AMS.Core.Constants;
using AMS.Core.Tests.Helpers;

namespace AMS.Core.Tests.Entities;

public class UserTests
{
    [Fact]
    public void User_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.Id.Should().NotBeEmpty();
        user.FirstName.Should().BeEmpty();
        user.LastName.Should().BeEmpty();
        user.Email.Should().BeEmpty();
        user.PasswordHash.Should().BeEmpty();
        user.IsActive.Should().BeTrue();
        user.Role.Should().Be(ApplicationConstants.Roles.User);
        user.IsDeleted.Should().BeFalse();
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void User_FullName_ShouldCombineFirstAndLastName()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser("<PERSON>", "Doe");

        // Act
        var fullName = user.FullName;

        // Assert
        fullName.Should().Be("<PERSON> Doe");
    }

    [Theory]
    [InlineData("", "Doe", "Doe")]
    [InlineData("John", "", "John")]
    [InlineData("", "", "")]
    public void User_FullName_ShouldHandleEmptyNames(string firstName, string lastName, string expected)
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser(firstName, lastName);

        // Act
        var fullName = user.FullName;

        // Assert
        fullName.Should().Be(expected);
    }

    [Fact]
    public void User_ShouldInheritFromBaseEntity()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.Should().BeAssignableTo<BaseEntity>();
    }

    [Fact]
    public void User_ShouldHaveUniqueId()
    {
        // Arrange & Act
        var user1 = new User();
        var user2 = new User();

        // Assert
        user1.Id.Should().NotBe(user2.Id);
    }

    [Theory]
    [InlineData(ApplicationConstants.Roles.Administrator)]
    [InlineData(ApplicationConstants.Roles.Manager)]
    [InlineData(ApplicationConstants.Roles.User)]
    public void User_ShouldAcceptValidRoles(string role)
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser(role: role);

        // Act & Assert
        user.Role.Should().Be(role);
    }

    [Fact]
    public void User_ShouldAllowNullableLastLoginAt()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        user.LastLoginAt = null;

        // Assert
        user.LastLoginAt.Should().BeNull();
    }

    [Fact]
    public void User_ShouldAllowSettingLastLoginAt()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        var loginTime = DateTime.UtcNow;

        // Act
        user.LastLoginAt = loginTime;

        // Assert
        user.LastLoginAt.Should().Be(loginTime);
    }

    [Fact]
    public void User_ShouldAllowDeactivation()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        user.IsActive.Should().BeTrue();

        // Act
        user.IsActive = false;

        // Assert
        user.IsActive.Should().BeFalse();
    }

    [Fact]
    public void User_ShouldAllowSoftDelete()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();
        user.IsDeleted.Should().BeFalse();

        // Act
        user.IsDeleted = true;
        user.UpdatedAt = DateTime.UtcNow;
        user.UpdatedBy = "TestDeleter";

        // Assert
        user.IsDeleted.Should().BeTrue();
        user.UpdatedAt.Should().NotBeNull();
        user.UpdatedBy.Should().Be("TestDeleter");
    }

    [Fact]
    public void User_ShouldMaintainAuditFields()
    {
        // Arrange
        var createdAt = DateTime.UtcNow.AddDays(-1);
        var updatedAt = DateTime.UtcNow;
        var user = TestDataBuilder.CreateValidUser();

        // Act
        user.CreatedAt = createdAt;
        user.UpdatedAt = updatedAt;
        user.CreatedBy = "TestCreator";
        user.UpdatedBy = "TestUpdater";

        // Assert
        user.CreatedAt.Should().Be(createdAt);
        user.UpdatedAt.Should().Be(updatedAt);
        user.CreatedBy.Should().Be("TestCreator");
        user.UpdatedBy.Should().Be("TestUpdater");
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void User_ShouldAcceptValidEmailFormats(string email)
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser(email: email);

        // Act & Assert
        user.Email.Should().Be(email);
    }

    [Fact]
    public void User_ShouldStorePasswordHash()
    {
        // Arrange
        var passwordHash = "hashed_password_123";
        var user = TestDataBuilder.CreateValidUser();

        // Act
        user.PasswordHash = passwordHash;

        // Assert
        user.PasswordHash.Should().Be(passwordHash);
        user.PasswordHash.Should().NotBeEmpty();
    }

    [Fact]
    public void User_CreatedAt_ShouldBeSetOnCreation()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void User_UpdatedAt_ShouldBeNullInitially()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.UpdatedAt.Should().BeNull();
    }

    [Fact]
    public void User_ShouldAllowNullableAuditFields()
    {
        // Arrange
        var user = TestDataBuilder.CreateValidUser();

        // Act
        user.CreatedBy = null;
        user.UpdatedBy = null;

        // Assert
        user.CreatedBy.Should().BeNull();
        user.UpdatedBy.Should().BeNull();
    }
}
