# AMS Application Environment Variables
# Copy this file to .env and update with your actual values

# Application Environment
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:7001;http://localhost:5001

# Database Configuration
# .NET Configuration uses double underscore (__) for nested configuration
ConnectionStrings__DefaultConnection=Host=localhost;Database=ams_development;Username=ams_user;Password=ams_dev_password

# JWT Configuration
# .NET Configuration uses double underscore (__) for nested configuration
JwtSettings__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long-and-secure
JwtSettings__Issuer=AMS-Development
JwtSettings__Audience=AMS-Development
JwtSettings__ExpirationMinutes=60

# Logging Configuration
# Use the hierarchical format for logging levels
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft.AspNetCore=Warning
Logging__LogLevel__Microsoft.EntityFrameworkCore=Information

# Development Features
ENABLE_SWAGGER=true
ENABLE_DETAILED_ERRORS=true
ENABLE_SENSITIVE_DATA_LOGGING=true

# CORS Settings (if needed)
ALLOWED_ORIGINS=https://localhost:3000,http://localhost:3000

# Security Settings (for future use)
PASSWORD_RESET_TOKEN_EXPIRY_HOURS=24
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# File Upload Settings (for future use)
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=.jpg,.jpeg,.png,.pdf,.doc,.docx
