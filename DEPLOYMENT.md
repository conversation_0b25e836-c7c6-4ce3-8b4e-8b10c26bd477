# Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the AMS API to various environments including development, staging, and production.

## Prerequisites

### System Requirements

- **.NET 8 Runtime** or SDK
- **PostgreSQL 12+** database server
- **Reverse Proxy** (Nginx, Apache, or IIS)
- **SSL Certificate** for HTTPS (production)
- **Monitoring Tools** (optional but recommended)

### Infrastructure Requirements

- **CPU**: 2+ cores recommended
- **RAM**: 4GB+ recommended
- **Storage**: 20GB+ available space
- **Network**: Stable internet connection
- **Firewall**: Configured for HTTP/HTTPS traffic

## Environment Configuration

### Development Environment

#### 1. Local Development Setup

```bash
# Clone repository
git clone <repository-url>
cd ams-web

# Restore dependencies
dotnet restore

# Setup database
sudo -u postgres psql
CREATE DATABASE ams_development;
CREATE USER ams_user WITH PASSWORD 'ams_dev_password';
GRANT ALL PRIVILEGES ON DATABASE ams_development TO ams_user;
\q

# Apply migrations
dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API

# Run application
dotnet run --project src/AMS.API
```

#### 2. Development Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ams_development;Username=ams_user;Password=ams_dev_password"
  },
  "JwtSettings": {
    "SecretKey": "development-secret-key-32-chars-minimum",
    "Issuer": "AMS-Dev",
    "Audience": "AMS-Dev",
    "ExpirationMinutes": 60
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### Staging Environment

#### 1. Staging Server Setup

```bash
# Install .NET 8 Runtime
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y aspnetcore-runtime-8.0

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Create application user
sudo useradd -m -s /bin/bash ams
sudo mkdir -p /var/www/ams
sudo chown ams:ams /var/www/ams
```

#### 2. Database Setup

```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create staging database
CREATE DATABASE ams_staging_db;
CREATE USER ams_staging_user WITH PASSWORD 'staging_secure_password';
GRANT ALL PRIVILEGES ON DATABASE ams_staging_db TO ams_staging_user;

-- Configure SSL (recommended)
ALTER SYSTEM SET ssl = on;
SELECT pg_reload_conf();
```

#### 3. Application Deployment

```bash
# Build and publish
dotnet publish src/AMS.API -c Release -o /var/www/ams

# Set permissions
sudo chown -R ams:ams /var/www/ams
sudo chmod +x /var/www/ams/AMS.API

# Create systemd service
sudo tee /etc/systemd/system/ams.service > /dev/null <<EOF
[Unit]
Description=AMS API
After=network.target

[Service]
Type=notify
User=ams
WorkingDirectory=/var/www/ams
ExecStart=/usr/bin/dotnet /var/www/ams/AMS.API.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=ams-api
Environment=ASPNETCORE_ENVIRONMENT=Staging
Environment=DOTNET_PRINT_TELEMETRY_MESSAGE=false

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable ams.service
sudo systemctl start ams.service
```

### Production Environment

#### 1. Production Server Setup

```bash
# Install .NET 8 Runtime (same as staging)
# Install PostgreSQL with SSL
# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

#### 2. SSL Certificate Setup

```bash
# Using Let's Encrypt with Certbot
sudo apt-get install -y certbot
sudo certbot certonly --standalone -d your-domain.com

# Or use existing SSL certificate
sudo mkdir -p /etc/ssl/ams
sudo cp your-certificate.crt /etc/ssl/ams/
sudo cp your-private-key.key /etc/ssl/ams/
sudo chmod 600 /etc/ssl/ams/*
```

#### 3. Reverse Proxy Configuration (Nginx)

```nginx
# /etc/nginx/sites-available/ams
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 4. Environment Variables (Production)

```bash
# Create environment file
sudo tee /var/www/ams/.env > /dev/null <<EOF
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://localhost:5000
ConnectionStrings__DefaultConnection=Host=localhost;Database=ams_prod_db;Username=ams_prod_user;Password=super_secure_password;SSL Mode=Require
JwtSettings__SecretKey=production-super-secret-key-256-bits-minimum
JwtSettings__Issuer=AMS-Production
JwtSettings__Audience=AMS-Client
JwtSettings__ExpirationMinutes=60
EOF

# Secure the file
sudo chown ams:ams /var/www/ams/.env
sudo chmod 600 /var/www/ams/.env
```

## Database Migration

### Automated Migration (Recommended)

```bash
# The application automatically applies migrations on startup
# Ensure proper backup before deployment
```

### Manual Migration

```bash
# Generate migration script
dotnet ef migrations script --project src/AMS.Infrastructure --startup-project src/AMS.API --output migration.sql

# Review and apply manually
psql -h localhost -U ams_prod_user -d ams_prod_db -f migration.sql
```

## Monitoring & Logging

### Application Monitoring

```bash
# Check service status
sudo systemctl status ams.service

# View logs
sudo journalctl -u ams.service -f

# Check application health
curl -k https://your-domain.com/health
```

### Database Monitoring

```sql
-- Check database connections
SELECT * FROM pg_stat_activity WHERE datname = 'ams_prod_db';

-- Monitor query performance
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
```

### Log Configuration

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "AMS": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/ams/ams-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  }
}
```

## Backup & Recovery

### Database Backup

```bash
# Create backup script
sudo tee /usr/local/bin/ams-backup.sh > /dev/null <<EOF
#!/bin/bash
BACKUP_DIR="/var/backups/ams"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U ams_prod_user -d ams_prod_db > $BACKUP_DIR/ams_development_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/ams_development_$DATE.sql

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

sudo chmod +x /usr/local/bin/ams-backup.sh

# Schedule daily backups
echo "0 2 * * * /usr/local/bin/ams-backup.sh" | sudo crontab -
```

### Application Backup

```bash
# Backup application files
sudo tar -czf /var/backups/ams/ams_app_$(date +%Y%m%d).tar.gz -C /var/www ams
```

## Performance Optimization

### Application Performance

```json
{
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxConcurrentUpgradedConnections": 100,
      "MaxRequestBodySize": 10485760
    }
  }
}
```

### Database Performance

```sql
-- Optimize PostgreSQL settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

## Security Hardening

### Server Security

```bash
# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

### Application Security

- Use environment variables for secrets
- Enable HTTPS only
- Implement rate limiting
- Regular security updates
- Monitor for vulnerabilities

## Troubleshooting

### Common Issues

#### Application Won't Start

```bash
# Check service status
sudo systemctl status ams.service

# Check logs
sudo journalctl -u ams.service -n 50

# Check configuration
dotnet --info
```

#### Database Connection Issues

```bash
# Test database connection
psql -h localhost -U ams_prod_user -d ams_prod_db -c "SELECT 1;"

# Check PostgreSQL status
sudo systemctl status postgresql
```

#### SSL Certificate Issues

```bash
# Check certificate validity
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout

# Renew Let's Encrypt certificate
sudo certbot renew
```

## Deployment Checklist

### Pre-Deployment

- [ ] Code review completed
- [ ] Tests passing
- [ ] Database backup created
- [ ] Environment variables configured
- [ ] SSL certificates valid

### Deployment

- [ ] Application deployed
- [ ] Database migrations applied
- [ ] Service started successfully
- [ ] Health checks passing
- [ ] Monitoring configured

### Post-Deployment

- [ ] Functionality testing
- [ ] Performance monitoring
- [ ] Log monitoring
- [ ] Security scan
- [ ] Documentation updated

## Rollback Procedures

### Application Rollback

```bash
# Stop current service
sudo systemctl stop ams.service

# Restore previous version
sudo cp -r /var/backups/ams/previous_version/* /var/www/ams/

# Start service
sudo systemctl start ams.service
```

### Database Rollback

```bash
# Restore from backup
psql -h localhost -U ams_prod_user -d ams_prod_db < /var/backups/ams/ams_development_backup.sql
```
